"""
ETF配对交易策略统计分析演示脚本

本脚本演示如何使用统计分析模块对ETF配对交易策略进行深入分析，
包括价差序列统计特性分析和回归参数时变性分析。

使用方法:
python statistical_analysis_demo.py
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings

# 添加src目录到路径
sys.path.append('src')

from src.analysis.statistical_analyzer import ETFPairsStatisticalAnalyzer
from src.data.loader import DataLoader

warnings.filterwarnings('ignore')

def load_sample_data():
    """
    加载示例数据进行分析
    
    Returns:
        pd.DataFrame: 价格数据
    """
    print("正在加载数据...")
    
    try:
        # 尝试使用现有的数据加载器
        data_loader = DataLoader()
        
        # 使用最近一年的数据进行分析
        end_date = datetime.now().strftime('%Y-%m-%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y-%m-%d')
        
        print(f"数据时间范围: {start_date} 到 {end_date}")
        
        # 加载数据
        data = data_loader.load_data(
            start_date=start_date,
            end_date=end_date,
            frequency='minute'
        )
        
        print(f"成功加载数据: {len(data)} 行, 列: {data.columns.tolist()}")
        return data
        
    except Exception as e:
        print(f"使用数据加载器失败: {str(e)}")
        print("尝试直接读取本地数据文件...")
        
        # 尝试直接读取本地数据文件
        data_files = ['data/518880.csv', 'data/518850.csv']
        dataframes = []
        
        for file_path in data_files:
            if os.path.exists(file_path):
                try:
                    df = pd.read_csv(file_path, encoding='utf-8')
                    if 'date' in df.columns:
                        df['date'] = pd.to_datetime(df['date'])
                        df.set_index('date', inplace=True)
                    
                    # 获取ETF代码
                    etf_code = os.path.basename(file_path).split('.')[0]
                    
                    # 使用收盘价
                    if 'close' in df.columns:
                        price_series = df['close']
                        price_series.name = etf_code
                        dataframes.append(price_series)
                        print(f"读取 {etf_code}: {len(price_series)} 个数据点")
                    
                except Exception as e:
                    print(f"读取文件 {file_path} 失败: {str(e)}")
        
        if len(dataframes) >= 2:
            # 合并数据
            combined_data = pd.concat(dataframes, axis=1).dropna()
            print(f"合并后数据: {len(combined_data)} 行")
            
            # 只取最近的数据进行分析（避免数据量过大）
            if len(combined_data) > 10000:
                combined_data = combined_data.tail(10000)
                print(f"截取最近 {len(combined_data)} 个数据点进行分析")
            
            return combined_data
        else:
            print("无法找到足够的数据文件")
            return None

def create_sample_data():
    """
    创建模拟数据用于演示

    Returns:
        pd.DataFrame: 模拟的价格数据
    """
    print("创建模拟数据进行演示...")

    # 生成时间序列 (使用较小的数据集)
    dates = pd.date_range(start='2024-01-01', end='2024-01-10', freq='1min')
    n = len(dates)

    # 生成相关的价格序列
    np.random.seed(42)

    # ETF1价格 (基础序列)
    returns1 = np.random.normal(0, 0.001, n)
    price1 = 100 * np.exp(np.cumsum(returns1))

    # ETF2价格 (与ETF1相关)
    beta = 0.8 + 0.1 * np.sin(np.arange(n) / 1000)  # 时变beta
    noise = np.random.normal(0, 0.0005, n)
    price2 = beta * price1 + noise + 20  # 添加常数项和噪声

    # 创建DataFrame
    data = pd.DataFrame({
        '518880': price1,
        '518850': price2
    }, index=dates)

    print(f"生成模拟数据: {len(data)} 个数据点")
    print(f"ETF1价格范围: {data['518880'].min():.2f} - {data['518880'].max():.2f}")
    print(f"ETF2价格范围: {data['518850'].min():.2f} - {data['518850'].max():.2f}")

    return data

def run_statistical_analysis_demo():
    """
    运行统计分析演示
    """
    print("=" * 80)
    print("ETF配对交易策略统计分析演示")
    print("=" * 80)
    
    # 1. 加载数据
    data = load_sample_data()
    
    if data is None or len(data) < 100:
        print("使用模拟数据进行演示...")
        data = create_sample_data()
    
    if data is None:
        print("无法获取数据，退出演示")
        return
    
    # 确保只有两列数据
    if len(data.columns) > 2:
        data = data.iloc[:, :2]
        print(f"使用前两列数据: {data.columns.tolist()}")
    
    # 2. 初始化统计分析器
    analyzer = ETFPairsStatisticalAnalyzer(output_dir="statistical_analysis_results")
    
    # 3. 设置分析参数
    analysis_params = {
        'window': 40,        # rolling regression窗口大小
        'std_dev_mult': 0.5  # 标准差倍数
    }
    
    print(f"\n分析参数:")
    print(f"- 回归窗口大小: {analysis_params['window']}")
    print(f"- 标准差倍数: {analysis_params['std_dev_mult']}")
    
    # 4. 运行完整分析
    try:
        results = analyzer.run_complete_analysis(
            price_data=data,
            window=analysis_params['window'],
            std_dev_mult=analysis_params['std_dev_mult']
        )
        
        # 5. 输出关键结果摘要
        print("\n" + "=" * 60)
        print("分析结果摘要")
        print("=" * 60)
        
        # 价差序列基本统计
        if 'spread_analysis' in results and 'distribution_analysis' in results['spread_analysis']:
            dist_stats = results['spread_analysis']['distribution_analysis'].get('descriptive_stats', {})
            print(f"\n价差序列统计:")
            print(f"- 样本数量: {dist_stats.get('count', 'N/A')}")
            print(f"- 均值: {dist_stats.get('mean', 0):.6f}")
            print(f"- 标准差: {dist_stats.get('std', 0):.6f}")
            print(f"- 偏度: {dist_stats.get('skewness', 0):.4f}")
            print(f"- 峰度: {dist_stats.get('kurtosis', 0):.4f}")
        
        # 白噪声检验结果
        if ('spread_analysis' in results and 
            'white_noise_tests' in results['spread_analysis'] and
            'ljung_box' in results['spread_analysis']['white_noise_tests']):
            
            ljung_box = results['spread_analysis']['white_noise_tests']['ljung_box']
            print(f"\n白噪声检验 (Ljung-Box):")
            for lag_key, result in list(ljung_box.items())[:3]:  # 只显示前3个
                if 'error' not in result:
                    lag = lag_key.split('_')[1]
                    print(f"- 滞后{lag}期: p值={result['p_value']:.4f}, "
                          f"白噪声={'是' if result['is_white_noise'] else '否'}")
        
        # 参数稳定性结果
        if 'parameter_analysis' in results and 'beta_analysis' in results['parameter_analysis']:
            beta_stats = results['parameter_analysis']['beta_analysis'].get('statistics', {})
            print(f"\nBeta系数稳定性:")
            print(f"- 变异系数: {beta_stats.get('coefficient_of_variation', 0):.4f}")
            print(f"- 范围比: {beta_stats.get('range_ratio', 0):.4f}")
        
        # 优化建议
        if 'optimization_suggestions' in results:
            suggestions = results['optimization_suggestions']
            print(f"\n优化建议:")
            
            if 'parameter_update_frequency' in suggestions:
                freq_suggestion = suggestions['parameter_update_frequency']
                print(f"- 参数更新频率: {freq_suggestion['suggested']} (当前: {freq_suggestion['current']})")
            
            if 'window_size' in suggestions:
                window_suggestion = suggestions['window_size']
                print(f"- 窗口大小: {window_suggestion['suggested']} (当前: {window_suggestion['current']})")
            
            if 'threshold_multiplier' in suggestions:
                threshold_suggestion = suggestions['threshold_multiplier']
                print(f"- 阈值倍数: {threshold_suggestion['suggested']} (当前: {threshold_suggestion['current']})")
        
        print(f"\n详细结果已保存至: {analyzer.output_dir}")
        print("- statistical_analysis_plots.png: 可视化图表")
        print("- statistical_analysis_report.md: 详细分析报告")
        
        return results
        
    except Exception as e:
        print(f"分析过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 运行演示
    results = run_statistical_analysis_demo()
    
    if results:
        print("\n演示完成！")
        print("请查看生成的图表和报告文件以获取详细分析结果。")
    else:
        print("\n演示失败，请检查错误信息。")
