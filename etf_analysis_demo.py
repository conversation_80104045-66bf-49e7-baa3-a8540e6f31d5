#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF配对交易策略统计分析演示脚本

简化版本，专注于核心分析功能的演示
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from scipy import stats
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.tsa.stattools import adfuller
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_signal_data(file_path):
    """加载信号数据"""
    print(f"正在加载信号数据: {file_path}")
    
    try:
        # 读取CSV文件
        df = pd.read_csv(file_path)
        
        # 转换时间列
        if 'datetime' in df.columns:
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
        
        # 过滤掉无效数据行
        df = df.dropna(subset=['ma', 'std', 'beta'])
        
        print(f"信号数据加载完成: {len(df)} 行有效数据")
        print(f"时间范围: {df.index[0]} 至 {df.index[-1]}")
        
        return df
        
    except Exception as e:
        print(f"加载信号数据时出错: {str(e)}")
        return None

def calculate_spread_series(signals_df):
    """计算价差序列"""
    print("\n=== 计算价差序列 ===")
    
    # 提取必要的列
    etf1_price = signals_df['etf1_price']
    etf2_price = signals_df['etf2_price'] 
    ma = signals_df['ma']  # beta值
    std = signals_df['std']  # beta的标准误差
    
    # 计算价差序列：etf1 - ma*etf2 - 0.5*std
    spread_series = etf1_price - ma * etf2_price - 0.5 * std
    spread_series = spread_series.dropna()
    
    print(f"价差序列计算完成: {len(spread_series)} 个有效数据点")
    print(f"价差统计: 均值={spread_series.mean():.6f}, 标准差={spread_series.std():.6f}")
    
    return spread_series, ma.dropna(), std.dropna()

def white_noise_analysis(spread_series):
    """白噪声检验分析"""
    print("\n=== 价差序列白噪声检验 ===")
    results = {}
    
    # 1. Ljung-Box检验
    print("1. Ljung-Box检验（序列相关性）")
    try:
        lags_to_test = [10, 20]
        for lag in lags_to_test:
            if len(spread_series) > lag:
                lb_result = acorr_ljungbox(spread_series, lags=lag, return_df=True)
                lb_stat = lb_result['lb_stat'].iloc[-1]
                lb_pvalue = lb_result['lb_pvalue'].iloc[-1]
                
                is_white_noise = lb_pvalue > 0.05
                print(f"   滞后{lag}期: 统计量={lb_stat:.4f}, p值={lb_pvalue:.4f}, "
                      f"白噪声={'是' if is_white_noise else '否'}")
                
                results[f'ljung_box_lag_{lag}'] = {
                    'statistic': float(lb_stat),
                    'p_value': float(lb_pvalue),
                    'is_white_noise': is_white_noise
                }
    except Exception as e:
        print(f"   Ljung-Box检验失败: {str(e)}")
    
    # 2. ADF平稳性检验
    print("2. ADF平稳性检验")
    try:
        adf_result = adfuller(spread_series.dropna())
        adf_stat, adf_pvalue = adf_result[0], adf_result[1]
        
        results['adf_test'] = {
            'statistic': float(adf_stat),
            'p_value': float(adf_pvalue),
            'is_stationary': adf_pvalue < 0.05
        }
        
        print(f"   ADF统计量: {adf_stat:.4f}")
        print(f"   p值: {adf_pvalue:.4f}")
        print(f"   平稳性: {'是' if adf_pvalue < 0.05 else '否'}")
        
    except Exception as e:
        print(f"   ADF检验失败: {str(e)}")
    
    return results

def distribution_analysis(spread_series):
    """分布特征分析"""
    print("\n=== 价差序列分布特征分析 ===")
    results = {}
    
    # 1. 描述性统计
    print("1. 描述性统计")
    desc_stats = spread_series.describe()
    skewness = stats.skew(spread_series.dropna())
    kurtosis = stats.kurtosis(spread_series.dropna())
    
    results['descriptive_stats'] = {
        'count': int(desc_stats['count']),
        'mean': float(desc_stats['mean']),
        'std': float(desc_stats['std']),
        'skewness': float(skewness),
        'kurtosis': float(kurtosis),
        'median': float(desc_stats['50%'])
    }
    
    print(f"   样本数量: {desc_stats['count']}")
    print(f"   均值: {desc_stats['mean']:.6f}")
    print(f"   标准差: {desc_stats['std']:.6f}")
    print(f"   偏度: {skewness:.4f}")
    print(f"   峰度: {kurtosis:.4f}")
    
    # 2. 正态性检验
    print("2. 正态性检验")
    try:
        # Kolmogorov-Smirnov检验
        mean, std = spread_series.mean(), spread_series.std()
        normalized_data = (spread_series - mean) / std
        ks_stat, ks_pvalue = stats.kstest(normalized_data, 'norm')
        
        results['ks_test'] = {
            'statistic': float(ks_stat),
            'p_value': float(ks_pvalue),
            'is_normal': ks_pvalue > 0.05
        }
        
        print(f"   Kolmogorov-Smirnov: 统计量={ks_stat:.4f}, p值={ks_pvalue:.4f}, "
              f"正态分布={'是' if ks_pvalue > 0.05 else '否'}")
    except Exception as e:
        print(f"   正态性检验失败: {str(e)}")
    
    return results

def parameter_stability_analysis(beta_series, std_error_series):
    """参数稳定性分析"""
    print("\n=== 回归参数时变性分析 ===")
    results = {}
    
    # 1. Beta系数分析
    print("1. Beta系数时变特征")
    beta_stats = {
        'mean': float(beta_series.mean()),
        'std': float(beta_series.std()),
        'cv': float(beta_series.std() / beta_series.mean()),  # 变异系数
        'range': float(beta_series.max() - beta_series.min())
    }
    
    print(f"   Beta均值: {beta_stats['mean']:.6f}")
    print(f"   Beta标准差: {beta_stats['std']:.6f}")
    print(f"   Beta变异系数: {beta_stats['cv']:.4f}")
    
    results['beta_analysis'] = beta_stats
    
    # 2. 标准误差分析
    print("2. 标准误差时变特征")
    std_error_stats = {
        'mean': float(std_error_series.mean()),
        'std': float(std_error_series.std()),
        'cv': float(std_error_series.std() / std_error_series.mean()),
        'range': float(std_error_series.max() - std_error_series.min())
    }
    
    print(f"   标准误差均值: {std_error_stats['mean']:.6f}")
    print(f"   标准误差变异系数: {std_error_stats['cv']:.4f}")
    
    results['std_error_analysis'] = std_error_stats
    
    return results

def generate_optimization_suggestions(analysis_results):
    """生成优化建议"""
    print("\n=== 生成优化建议 ===")
    suggestions = {}
    
    # 参数更新频率建议
    if 'beta_analysis' in analysis_results:
        beta_cv = analysis_results['beta_analysis'].get('cv', 0)
        
        if beta_cv < 0.01:
            update_freq = "5-10分钟"
            reason = "Beta系数非常稳定，可以降低更新频率"
        elif beta_cv < 0.05:
            update_freq = "2-5分钟"
            reason = "Beta系数较稳定，可以适当降低更新频率"
        else:
            update_freq = "1分钟"
            reason = "Beta系数变化较大，建议保持高频更新"
        
        suggestions['parameter_update_frequency'] = {
            'recommended_frequency': update_freq,
            'reason': reason,
            'beta_cv': beta_cv
        }
        
        print(f"   参数更新频率建议: {update_freq}")
        print(f"   理由: {reason}")
    
    return suggestions

def create_visualization(spread_series, beta_series, std_error_series, output_dir="statistical_analysis_results"):
    """创建可视化图表"""
    print("\n=== 生成可视化图表 ===")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('ETF配对交易策略统计分析结果', fontsize=16, fontweight='bold')
    
    # 1. 价差序列时间序列图
    axes[0, 0].plot(spread_series.index, spread_series.values, alpha=0.7, linewidth=0.8, color='blue')
    axes[0, 0].set_title('价差序列时间序列')
    axes[0, 0].set_xlabel('时间')
    axes[0, 0].set_ylabel('价差')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 价差序列分布直方图
    axes[0, 1].hist(spread_series.dropna(), bins=50, alpha=0.7, density=True, color='skyblue', edgecolor='black')
    axes[0, 1].set_title('价差序列分布')
    axes[0, 1].set_xlabel('价差值')
    axes[0, 1].set_ylabel('密度')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. Beta系数时间序列
    axes[1, 0].plot(beta_series.index, beta_series.values, alpha=0.7, linewidth=0.8, color='orange')
    axes[1, 0].set_title('Beta系数时间序列')
    axes[1, 0].set_xlabel('时间')
    axes[1, 0].set_ylabel('Beta值')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 标准误差时间序列
    axes[1, 1].plot(std_error_series.index, std_error_series.values, alpha=0.7, linewidth=0.8, color='purple')
    axes[1, 1].set_title('标准误差时间序列')
    axes[1, 1].set_xlabel('时间')
    axes[1, 1].set_ylabel('标准误差')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    plot_file = os.path.join(output_dir, 'etf_analysis_plots.png')
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"可视化图表已保存: {plot_file}")
    
    plt.show()
    return plot_file

def main():
    """主函数"""
    print("ETF配对交易策略统计分析演示")
    print("=" * 50)
    
    # 查找信号文件
    results_dir = "results"
    if not os.path.exists(results_dir):
        print(f"错误: 结果目录 {results_dir} 不存在")
        return
    
    signal_files = [f for f in os.listdir(results_dir) if f.startswith('signals_') and f.endswith('.csv')]
    
    if not signal_files:
        print(f"错误: 在 {results_dir} 目录中未找到信号文件")
        return
    
    # 选择第一个信号文件进行演示
    selected_file = signal_files[0]
    signals_file_path = os.path.join(results_dir, selected_file)
    print(f"使用信号文件: {signals_file_path}")
    
    # 加载数据
    signals_df = load_signal_data(signals_file_path)
    if signals_df is None:
        return
    
    # 计算价差序列
    spread_series, beta_series, std_error_series = calculate_spread_series(signals_df)
    
    # 执行各项分析
    white_noise_results = white_noise_analysis(spread_series)
    distribution_results = distribution_analysis(spread_series)
    parameter_results = parameter_stability_analysis(beta_series, std_error_series)
    
    # 生成优化建议
    suggestions = generate_optimization_suggestions(parameter_results)
    
    # 生成可视化图表
    create_visualization(spread_series, beta_series, std_error_series)
    
    print("\n" + "=" * 50)
    print("分析完成！")
    
    # 显示关键结果摘要
    print("\n关键结果摘要:")
    if 'descriptive_stats' in distribution_results:
        stats_data = distribution_results['descriptive_stats']
        print(f"价差序列: 均值={stats_data['mean']:.6f}, 标准差={stats_data['std']:.6f}")
    
    if 'beta_analysis' in parameter_results:
        beta_cv = parameter_results['beta_analysis']['cv']
        stability = "高" if beta_cv < 0.01 else "中" if beta_cv < 0.05 else "低"
        print(f"Beta系数稳定性: {stability} (变异系数: {beta_cv:.4f})")
    
    if 'parameter_update_frequency' in suggestions:
        freq = suggestions['parameter_update_frequency']['recommended_frequency']
        print(f"建议参数更新频率: {freq}")

if __name__ == "__main__":
    main()
