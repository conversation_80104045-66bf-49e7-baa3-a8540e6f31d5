# This file auto-generated by tools/get-R-bs-test-vectors.R
# Using: R version 2.15.1 (2012-06-22)
import numpy as np

R_bs_test_x = np.array(
    [
        1,
        1.5,
        2.25,
        3.375,
        5.0625,
        7.59375,
        11.390625,
        17.0859375,
        25.62890625,
        38.443359375,
        57.6650390625,
        86.49755859375,
        129.746337890625,
        194.6195068359375,
        291.92926025390625,
        437.893890380859375,
        656.8408355712890625,
        985.26125335693359375,
        1477.8918800354003906,
        2216.8378200531005859,
    ]
)
R_bs_test_data = """
--BEGIN TEST CASE--
degree=1
df=3
intercept=TRUE
Boundary.knots=None
knots=None
output=np.array([1, 0.98937395581474985029, 0.97343488953687462573, 0.94952629012006184439, 0.91366339099484261688, 0.85986904230701377561, 0.77917751927527056921, 0.65814023472765570411, 0.47658430790623346196, 0.20425041767410004323, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.010626044185250137566, 0.026565110463125343049, 0.050473709879938155609, 0.086336609005157369245, 0.14013095769298619664, 0.22082248072472943079, 0.34185976527234429589, 0.52341569209376659355, 0.79574958232589998453, 0.99556855753085560234, 0.98227423012342263142, 0.96233273901227300851, 0.93242050234554862964, 0.88755214734546206135, 0.82024961484533231992, 0.71929581609513748575, 0.56786511796984540101, 0.34071907078190727391, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0044314424691443508181, 0.017725769876577403272, 0.037667260987726984556, 0.067579497654451342603, 0.11244785265453789702, 0.17975038515466773559, 0.28070418390486245874, 0.43213488203015459899, 0.6592809292180927816, 1, ]).reshape((20, 3, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=5
intercept=TRUE
Boundary.knots=None
knots=None
output=np.array([1, 0.9161205766710354137, 0.79030144167758842322, 0.60157273918741804852, 0.31847968545216254199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.083879423328964614059, 0.20969855832241152127, 0.39842726081258189597, 0.68152031454783745801, 0.9846005774783446185, 0.89220404234841199642, 0.7536092396535130078, 0.54571703561116458037, 0.23387872954764196698, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.015399422521655438748, 0.10779595765158807297, 0.24639076034648701996, 0.45428296438883541963, 0.76612127045235811629, 0.96572040707016604255, 0.86288162828066417021, 0.7086234600964114172, 0.47723620782003217666, 0.13015532940546331586, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.034279592929833957449, 0.13711837171933582979, 0.29137653990358863831, 0.52276379217996793436, 0.86984467059453673965, 0.94202898550724645244, 0.82608695652173913526, 0.65217391304347827052, 0.39130434782608697342, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.057971014492753623892, 0.17391304347826089249, 0.34782608695652178499, 0.60869565217391308209, 1, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=12
intercept=TRUE
Boundary.knots=None
knots=None
output=np.array([1, 0.52173913043478281626, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.47826086956521718374, 0.90243902439024414885, 0.36585365853658557977, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.097560975609755906657, 0.63414634146341442023, 0.77777777777777779011, 0.16666666666666651864, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.22222222222222218213, 0.83333333333333348136, 0.625, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.37499999999999994449, 0.96992481203007530066, 0.47368421052631592971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.030075187969924695869, 0.52631578947368407029, 0.86440677966101697738, 0.30508474576271171763, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13559322033898302262, 0.69491525423728828237, 0.72815533980582491935, 0.087378640776698143777, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.27184466019417513616, 0.91262135922330189786, 0.57446808510638269762, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.42553191489361730238, 0.9375, 0.4218750000000004996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.062499999999999958367, 0.57812499999999944489, 0.82300884955752262595, 0.23893805309734547637, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17699115044247745732, 0.76106194690265460689, 0.67346938775510212238, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.32653061224489787762, 1, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([])
output=np.array([1, 0.99977435171677508929, 0.9994358792919375567, 0.99892817065468131332, 0.99816660769879694826, 0.99702426326497040066, 0.99531074661423057925, 0.99274047163812084715, 0.98888505917395630451, 0.98310194047770937953, 0.97442726243333910308, 0.96141524536678357737, 0.94189721976695039984, 0.91262018136720068906, 0.86870462376757595635, 0.8028312873681389128, 0.70402128276898334747, 0.55580627587024999947, 0.33348376552215003299, 0, 0, 0.00022564828322499611425, 0.00056412070806249024497, 0.001071829345318731563, 0.0018333923012030933775, 0.0029757367350296362075, 0.0046892533857694502358, 0.0072595283618791719288, 0.011114940826043754468, 0.01689805952229062741, 0.025572737566660935088, 0.038584754633216401809, 0.058102780233049600156, 0.087379818632799394207, 0.13129537623242407141, 0.19716871263186111496, 0.29597871723101670804, 0.44419372412975000053, 0.66651623447785002252, 1, ]).reshape((20, 2, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([100, ])
output=np.array([1, 0.994949494949495028, 0.98737373737373745897, 0.97601010101010110542, 0.9589646464646465196, 0.93339646464646475188, 0.89504419191919204479, 0.83751578282828287314, 0.75122316919191922668, 0.62178424873737381251, 0.42762586805555558023, 0.13638829703282828731, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0050505050505050509344, 0.01262626262626262777, 0.023989898989898991721, 0.041035353535353535914, 0.066603535353535359143, 0.10495580808080809398, 0.16248421717171718237, 0.24877683080808082883, 0.37821575126262629851, 0.5723741319444445308, 0.86361170296717182371, 0.98594774828339049044, 0.95530148510216805757, 0.90933209033033446378, 0.84037799817258396207, 0.7369468599359582095, 0.58180015258101969167, 0.3490800915486118039, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.014052251716609454046, 0.044698514897831886916, 0.090667909669665536221, 0.15962200182741601018, 0.26305314006404173499, 0.41819984741898030833, 0.65091990845138814059, 1, ]).reshape((20, 3, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([1000, ])
output=np.array([1, 0.99949949949949945527, 0.99874874874874874919, 0.99762262262262257906, 0.99593343343343343488, 0.9933996496496496631, 0.98959897397397400542, 0.98389796046046040789, 0.97534644019019023364, 0.96251915978478475022, 0.94327823917667663611, 0.91441685826451446495, 0.87112478689627126371, 0.80618667984390635084, 0.70877951926535909255, 0.5626687783975381496, 0.34350266709580673519, 0.014753500143209615295, 0, 0, 0, 0.00050050050050050049616, 0.0012512512512512512404, 0.0023773773773773775736, 0.0040665665665665668566, 0.0066003503503503499136, 0.0104010260260260258, 0.016102039539539540064, 0.02465355980980980799, 0.037480840215215215083, 0.056721760823323322254, 0.08558314173548547954, 0.12887521310372873629, 0.19381332015609359365, 0.29122048073464090745, 0.4373312216024618504, 0.6564973329041932093, 0.98524649985679035868, 0.60726740066762052717, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39273259933237941732, 1, ]).reshape((20, 3, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([10, 100, 1000, ])
output=np.array([1, 0.94444444444444441977, 0.86111111111111104943, 0.73611111111111104943, 0.54861111111111104943, 0.26736111111111110494, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.055555555555555552472, 0.13888888888888889506, 0.26388888888888889506, 0.45138888888888883955, 0.73263888888888883955, 0.98454861111111113825, 0.92126736111111118266, 0.82634548611111113825, 0.68396267361111118266, 0.47038845486111113825, 0.15002712673611112715, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.015451388888888889506, 0.07873263888888888673, 0.17365451388888888951, 0.31603732638888892836, 0.52961154513888886175, 0.84997287326388892836, 0.96694851345486110272, 0.89486721462673612937, 0.78674526638454855831, 0.62456234402126731275, 0.38128796047634549993, 0.016376385158962673133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.033051486545138890338, 0.10513278537326388451, 0.21325473361545138618, 0.37543765597873263173, 0.61871203952365450007, 0.98362361484103733034, 0.60726740066762052717, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39273259933237941732, 1, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=3
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.97919016410100090386, 0.9687852461515014113, 0.95317786922725200593, 0.92976680384087795339, 0.89465020576131693009, 0.84197530864197533962, 0.76296296296296306494, 0.64444444444444448639, 0.46666666666666667407, 0.2000000000000000111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.020809835898999137771, 0.031214753848498706656, 0.046822130772748063454, 0.070233196159122088242, 0.1053497942386831393, 0.15802469135802471589, 0.23703703703703704608, 0.35555555555555556912, 0.53333333333333332593, 0.80000000000000004441, 0.99674423566950098863, 0.98697694267800384349, 0.9723260031907582368, 0.95034959395988971576, 0.9173849801135870452, 0.86793805934413292835, 0.7937676781899518641, 0.68251210645868021221, 0.51562874886177267886, 0.26530371246641143435, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0032557643304990334897, 0.013023057321996133959, 0.027673996809241787481, 0.049650406040110263428, 0.082615019886412982553, 0.13206194065586704389, 0.20623232181004816366, 0.3174878935413198433, 0.48437125113822732114, 0.73469628753358862117, ]).reshape((20, 3, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=5
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.85634118967452310667, 0.78451178451178460449, 0.67676767676767679571, 0.51515151515151524908, 0.2727272727272727626, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.14365881032547700435, 0.21548821548821550653, 0.3232323232323232598, 0.48484848484848486194, 0.72727272727272729291, 0.9846005774783446185, 0.89220404234841199642, 0.7536092396535130078, 0.54571703561116458037, 0.23387872954764196698, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.015399422521655438748, 0.10779595765158807297, 0.24639076034648701996, 0.45428296438883541963, 0.76612127045235811629, 0.96572040707016604255, 0.86288162828066417021, 0.7086234600964114172, 0.47723620782003217666, 0.13015532940546331586, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.034279592929833957449, 0.13711837171933582979, 0.29137653990358863831, 0.52276379217996793436, 0.86984467059453673965, 0.9590229415870602514, 0.8770688247611807542, 0.7541376495223615084, 0.56974088666413258419, 0.29314574237678914237, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.040977058412939762477, 0.12293117523881928743, 0.24586235047763857486, 0.43025911333586747132, 0.70685425762321085763, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=12
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.51111111111111118266, 0.2666666666666668295, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.48888888888888881734, 0.7333333333333331705, 0.90243902439024414885, 0.36585365853658557977, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.097560975609755906657, 0.63414634146341442023, 0.77777777777777779011, 0.16666666666666651864, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.22222222222222218213, 0.83333333333333348136, 0.625, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.37499999999999994449, 0.96992481203007530066, 0.47368421052631592971, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.030075187969924695869, 0.52631578947368407029, 0.86440677966101697738, 0.30508474576271171763, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13559322033898302262, 0.69491525423728828237, 0.72815533980582491935, 0.087378640776698143777, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.27184466019417513616, 0.91262135922330189786, 0.57446808510638269762, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.42553191489361730238, 0.9375, 0.4218750000000004996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.062499999999999958367, 0.57812499999999944489, 0.82300884955752262595, 0.23893805309734547637, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17699115044247745732, 0.76106194690265460689, 0.80946623645948467818, 0.41649034915717175753, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.19053376354051526631, 0.58350965084282824247, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([])
output=np.array([0.99966666666666659236, 0.99949999999999994404, 0.99924999999999997158, 0.99887499999999995737, 0.99831249999999993605, 0.9974687500000000151, 0.99620312499999996714, 0.99430468750000000622, 0.99145703124999995381, 0.98718554687499993072, 0.98077832031250000711, 0.97116748046875001066, 0.95675122070312501599, 0.93512683105468752398, 0.90269024658203123046, 0.85403536987304684569, 0.78105305480957032405, 0.67157958221435543056, 0.50736937332153320135, 0.26105405998229980202, 0.0003333333333333333222, 0.00050000000000000001041, 0.00075000000000000001561, 0.001124999999999999915, 0.0016874999999999999809, 0.0025312500000000000798, 0.0037968749999999999029, 0.0056953124999999998543, 0.0085429687499999993477, 0.012814453124999999889, 0.019221679687499999833, 0.02883251953124999975, 0.043248779296874997891, 0.064873168945312503775, 0.097309753417968741784, 0.14596463012695312655, 0.21894694519042967595, 0.32842041778564451393, 0.49263062667846679865, 0.73894594001770019798, ]).reshape((20, 2, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([100, ])
output=np.array([0.98999999999999999112, 0.98499999999999998668, 0.97750000000000003553, 0.96625000000000005329, 0.94937499999999996891, 0.92406250000000000888, 0.88609375000000001332, 0.82914062499999996447, 0.74371093750000005773, 0.61556640625000003109, 0.42334960937499999112, 0.13502441406250001443, 0, 0, 0, 0, 0, 0, 0, 0, 0.010000000000000000208, 0.014999999999999999445, 0.022499999999999999167, 0.03375000000000000222, 0.050625000000000003331, 0.075937500000000004996, 0.11390625000000000056, 0.17085937500000000777, 0.25628906249999999778, 0.38443359375000002442, 0.57665039062500000888, 0.86497558593750001332, 0.98974264210668094766, 0.96737258384967661495, 0.93381749646417022692, 0.88348486538591053385, 0.80798591876852099425, 0.69473749884243662933, 0.52486486895331019298, 0.27005592411962048294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.010257357893318963873, 0.032627416150323274024, 0.066182503535829731445, 0.11651513461408942451, 0.192014081231478978, 0.30526250115756325965, 0.47513513104668975151, 0.72994407588037946155, ]).reshape((20, 3, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([1000, ])
output=np.array([0.99899999999999999911, 0.99850000000000005418, 0.99775000000000002576, 0.99662499999999998312, 0.9949375000000000302, 0.9924062500000000453, 0.98860937500000001243, 0.98291406250000001865, 0.97437109374999997247, 0.96155664062500001421, 0.94233496093750002132, 0.91350244140625003197, 0.87025366210937504796, 0.80538049316406257194, 0.7080707397460938024, 0.56210610961914064809, 0.34315916442871097214, 0.014738746643066406167, 0, 0, 0.0010000000000000000208, 0.0015000000000000000312, 0.0022500000000000002637, 0.0033749999999999999618, 0.0050625000000000001596, 0.0075937499999999998057, 0.011390624999999999709, 0.017085937499999998695, 0.025628906249999999778, 0.038443359374999999667, 0.0576650390624999995, 0.086497558593749995781, 0.12974633789062500755, 0.19461950683593751132, 0.29192926025390625311, 0.43789389038085940742, 0.65684083557128902786, 0.9852612533569335973, 0.76105405998229980202, 0.39158108997344970303, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23894594001770019798, 0.60841891002655035248, ]).reshape((20, 3, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([10, 100, 1000, ])
output=np.array([0.9000000000000000222, 0.85000000000000008882, 0.7750000000000000222, 0.66250000000000008882, 0.4937500000000000222, 0.24062500000000000555, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10000000000000000555, 0.1500000000000000222, 0.22500000000000000555, 0.3375000000000000222, 0.5062499999999999778, 0.7593750000000000222, 0.98454861111111113825, 0.92126736111111118266, 0.82634548611111113825, 0.68396267361111118266, 0.47038845486111113825, 0.15002712673611112715, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.015451388888888889506, 0.07873263888888888673, 0.17365451388888888951, 0.31603732638888892836, 0.52961154513888886175, 0.84997287326388892836, 0.96694851345486110272, 0.89486721462673612937, 0.78674526638454855831, 0.62456234402126731275, 0.38128796047634549993, 0.016376385158962673133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.033051486545138890338, 0.10513278537326388451, 0.21325473361545138618, 0.37543765597873263173, 0.61871203952365450007, 0.98362361484103733034, 0.76105405998229980202, 0.39158108997344970303, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23894594001770019798, 0.60841891002655035248, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=3
intercept=FALSE
Boundary.knots=None
knots=None
output=np.array([0, 0.040686586141131603211, 0.10171646535282900803, 0.19326128417037510832, 0.33057851239669427956, 0.53655435473617296704, 0.84551811824539113704, 0.97622585438335818253, 0.92273402674591387118, 0.84249628528974740416, 0.72213967310549775913, 0.54160475482912329159, 0.27080237741456153477, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.023774145616641918083, 0.077265973254086212085, 0.1575037147102526236, 0.27786032689450229638, 0.45839524517087676392, 0.72919762258543852074, 0.98941973879980160689, 0.9418085633989089489, 0.87039180029756990642, 0.76326665564556128718, 0.60257893866754841383, 0.3615473632005290483, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.010580261200198394847, 0.058191436601091106606, 0.12960819970243014909, 0.23673334435443876833, 0.39742106133245164168, 0.63845263679947106272, 1, ]).reshape((20, 3, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=5
intercept=FALSE
Boundary.knots=None
knots=None
output=np.array([0, 0.1342281879194630323, 0.33557046979865756686, 0.63758389261744941034, 0.98069963811821481148, 0.83594692400482528694, 0.61881785283474100012, 0.2931242460796145699, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.019300361881785188523, 0.16405307599517471306, 0.38118214716525899988, 0.70687575392038548561, 0.9581151832460734763, 0.80104712041884851281, 0.56544502617801106759, 0.21204188481675489975, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.041884816753926495947, 0.19895287958115145943, 0.4345549738219888769, 0.78795811518324510025, 0.93133047210300479168, 0.75965665236051571618, 0.50214592274678215844, 0.11587982832618169693, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.068669527896995374849, 0.2403433476394844226, 0.49785407725321800809, 0.88412017167381840022, 0.89905362776025266047, 0.70977917981072580211, 0.42586750788643545906, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10094637223974732565, 0.29022082018927419789, 0.57413249211356454094, 1, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=12
intercept=FALSE
Boundary.knots=None
knots=None
output=np.array([0, 0.53333333333333343695, 0.81818181818181801024, 0.16363636363636296922, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.18181818181818207303, 0.83636363636363708629, 0.57446808510638280865, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.42553191489361724686, 0.9000000000000000222, 0.29999999999999982236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10000000000000003331, 0.70000000000000017764, 0.67346938775510178932, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.32653061224489815517, 0.96923076923076900702, 0.41538461538461529665, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.030769230769230916656, 0.58461538461538464784, 0.7714285714285711304, 0.085714285714284479956, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.22857142857142889736, 0.91428571428571558943, 0.5217391304347820391, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.47826086956521790539, 0.86086956521739110837, 0.23478260869565212299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13913043478260883612, 0.76521739130434784926, 0.62499999999999966693, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.37500000000000027756, 0.93599999999999949907, 0.35999999999999854339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.064000000000000500933, 0.64000000000000145661, 0.7199999999999991962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2800000000000008038, 1, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([])
output=np.array([0, 0.00022564828322499611425, 0.00056412070806249024497, 0.001071829345318731563, 0.0018333923012030933775, 0.0029757367350296362075, 0.0046892533857694502358, 0.0072595283618791719288, 0.011114940826043754468, 0.01689805952229062741, 0.025572737566660935088, 0.038584754633216401809, 0.058102780233049600156, 0.087379818632799394207, 0.13129537623242407141, 0.19716871263186111496, 0.29597871723101670804, 0.44419372412975000053, 0.66651623447785002252, 1, ]).reshape((20, 1, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([100, ])
output=np.array([0, 0.0050505050505050509344, 0.01262626262626262777, 0.023989898989898991721, 0.041035353535353535914, 0.066603535353535359143, 0.10495580808080809398, 0.16248421717171718237, 0.24877683080808082883, 0.37821575126262629851, 0.5723741319444445308, 0.86361170296717182371, 0.98594774828339049044, 0.95530148510216805757, 0.90933209033033446378, 0.84037799817258396207, 0.7369468599359582095, 0.58180015258101969167, 0.3490800915486118039, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.014052251716609454046, 0.044698514897831886916, 0.090667909669665536221, 0.15962200182741601018, 0.26305314006404173499, 0.41819984741898030833, 0.65091990845138814059, 1, ]).reshape((20, 2, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([1000, ])
output=np.array([0, 0.00050050050050050049616, 0.0012512512512512512404, 0.0023773773773773775736, 0.0040665665665665668566, 0.0066003503503503499136, 0.0104010260260260258, 0.016102039539539540064, 0.02465355980980980799, 0.037480840215215215083, 0.056721760823323322254, 0.08558314173548547954, 0.12887521310372873629, 0.19381332015609359365, 0.29122048073464090745, 0.4373312216024618504, 0.6564973329041932093, 0.98524649985679035868, 0.60726740066762052717, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39273259933237941732, 1, ]).reshape((20, 2, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([10, 100, 1000, ])
output=np.array([0, 0.055555555555555552472, 0.13888888888888889506, 0.26388888888888889506, 0.45138888888888883955, 0.73263888888888883955, 0.98454861111111113825, 0.92126736111111118266, 0.82634548611111113825, 0.68396267361111118266, 0.47038845486111113825, 0.15002712673611112715, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.015451388888888889506, 0.07873263888888888673, 0.17365451388888888951, 0.31603732638888892836, 0.52961154513888886175, 0.84997287326388892836, 0.96694851345486110272, 0.89486721462673612937, 0.78674526638454855831, 0.62456234402126731275, 0.38128796047634549993, 0.016376385158962673133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.033051486545138890338, 0.10513278537326388451, 0.21325473361545138618, 0.37543765597873263173, 0.61871203952365450007, 0.98362361484103733034, 0.60726740066762052717, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.39273259933237941732, 1, ]).reshape((20, 4, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=3
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.075249853027630819735, 0.1128747795414462296, 0.16931216931216935828, 0.25396825396825400967, 0.38095238095238104226, 0.57142857142857150787, 0.85714285714285731732, 0.97622585438335818253, 0.92273402674591387118, 0.84249628528974740416, 0.72213967310549775913, 0.54160475482912329159, 0.27080237741456153477, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.023774145616641918083, 0.077265973254086212085, 0.1575037147102526236, 0.27786032689450229638, 0.45839524517087676392, 0.72919762258543852074, 0.99235077739698696053, 0.95792927568342844946, 0.90629702311309068286, 0.82884864425758408846, 0.71267607597432414135, 0.53841722354943410966, 0.27702894491209917316, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0076492226030130125794, 0.042070724316571522783, 0.093702976886909289389, 0.1711513557424159393, 0.28732392402567591416, 0.46158277645056589034, 0.72297105508790082684, ]).reshape((20, 3, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=5
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.21164021164021157295, 0.31746031746031733167, 0.47619047619047605302, 0.71428571428571407953, 0.98069963811821481148, 0.83594692400482528694, 0.61881785283474100012, 0.2931242460796145699, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.019300361881785188523, 0.16405307599517471306, 0.38118214716525899988, 0.70687575392038548561, 0.9581151832460734763, 0.80104712041884851281, 0.56544502617801106759, 0.21204188481675489975, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.041884816753926495947, 0.19895287958115145943, 0.4345549738219888769, 0.78795811518324510025, 0.93133047210300479168, 0.75965665236051571618, 0.50214592274678215844, 0.11587982832618169693, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.068669527896995374849, 0.2403433476394844226, 0.49785407725321800809, 0.88412017167381840022, 0.93044657380826634174, 0.80003389969876526067, 0.60441488853451352803, 0.31098637178813609561, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.069553426191733672135, 0.19996610030123476709, 0.39558511146548641646, 0.68901362821186384888, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=12
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.51612903225806461283, 0.77419354838709697475, 0.81818181818181801024, 0.16363636363636296922, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.18181818181818207303, 0.83636363636363708629, 0.57446808510638280865, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.42553191489361724686, 0.9000000000000000222, 0.29999999999999982236, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10000000000000003331, 0.70000000000000017764, 0.67346938775510178932, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.32653061224489815517, 0.96923076923076900702, 0.41538461538461529665, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.030769230769230916656, 0.58461538461538464784, 0.7714285714285711304, 0.085714285714284479956, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.22857142857142889736, 0.91428571428571558943, 0.5217391304347820391, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.47826086956521790539, 0.86086956521739110837, 0.23478260869565212299, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13913043478260883612, 0.76521739130434784926, 0.62499999999999966693, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.37500000000000027756, 0.93599999999999949907, 0.35999999999999854339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.064000000000000500933, 0.64000000000000145661, 0.84118724544512846197, 0.43281159087546033915, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.15881275455487159354, 0.56718840912453971637, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([])
output=np.array([0.0003333333333333333222, 0.00050000000000000001041, 0.00075000000000000001561, 0.001124999999999999915, 0.0016874999999999999809, 0.0025312500000000000798, 0.0037968749999999999029, 0.0056953124999999998543, 0.0085429687499999993477, 0.012814453124999999889, 0.019221679687499999833, 0.02883251953124999975, 0.043248779296874997891, 0.064873168945312503775, 0.097309753417968741784, 0.14596463012695312655, 0.21894694519042967595, 0.32842041778564451393, 0.49263062667846679865, 0.73894594001770019798, ]).reshape((20, 1, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([100, ])
output=np.array([0.010000000000000000208, 0.014999999999999999445, 0.022499999999999999167, 0.03375000000000000222, 0.050625000000000003331, 0.075937500000000004996, 0.11390625000000000056, 0.17085937500000000777, 0.25628906249999999778, 0.38443359375000002442, 0.57665039062500000888, 0.86497558593750001332, 0.98974264210668094766, 0.96737258384967661495, 0.93381749646417022692, 0.88348486538591053385, 0.80798591876852099425, 0.69473749884243662933, 0.52486486895331019298, 0.27005592411962048294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.010257357893318963873, 0.032627416150323274024, 0.066182503535829731445, 0.11651513461408942451, 0.192014081231478978, 0.30526250115756325965, 0.47513513104668975151, 0.72994407588037946155, ]).reshape((20, 2, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([1000, ])
output=np.array([0.0010000000000000000208, 0.0015000000000000000312, 0.0022500000000000002637, 0.0033749999999999999618, 0.0050625000000000001596, 0.0075937499999999998057, 0.011390624999999999709, 0.017085937499999998695, 0.025628906249999999778, 0.038443359374999999667, 0.0576650390624999995, 0.086497558593749995781, 0.12974633789062500755, 0.19461950683593751132, 0.29192926025390625311, 0.43789389038085940742, 0.65684083557128902786, 0.9852612533569335973, 0.76105405998229980202, 0.39158108997344970303, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23894594001770019798, 0.60841891002655035248, ]).reshape((20, 2, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=1
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([10, 100, 1000, ])
output=np.array([0.10000000000000000555, 0.1500000000000000222, 0.22500000000000000555, 0.3375000000000000222, 0.5062499999999999778, 0.7593750000000000222, 0.98454861111111113825, 0.92126736111111118266, 0.82634548611111113825, 0.68396267361111118266, 0.47038845486111113825, 0.15002712673611112715, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.015451388888888889506, 0.07873263888888888673, 0.17365451388888888951, 0.31603732638888892836, 0.52961154513888886175, 0.84997287326388892836, 0.96694851345486110272, 0.89486721462673612937, 0.78674526638454855831, 0.62456234402126731275, 0.38128796047634549993, 0.016376385158962673133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.033051486545138890338, 0.10513278537326388451, 0.21325473361545138618, 0.37543765597873263173, 0.61871203952365450007, 0.98362361484103733034, 0.76105405998229980202, 0.39158108997344970303, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.23894594001770019798, 0.60841891002655035248, ]).reshape((20, 4, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=5
intercept=TRUE
Boundary.knots=None
knots=None
output=np.array([1, 0.96845940607276881362, 0.92240303675860391142, 0.85609306993676004272, 0.76270864919645953162, 0.63576547531534310931, 0.47305239057526615731, 0.28507250058887945166, 0.10824783418564158655, 0.0085209665393945269174, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.031533426700978403612, 0.077552412011353655252, 0.14374742102560256196, 0.23683044051100490823, 0.36304322063581223601, 0.52407464745263665495, 0.70834870097260460575, 0.8774087197542927985, 0.96206652916722557034, 0.94530079752693674244, 0.90793500327984399956, 0.85375309463923021447, 0.77659028109030259213, 0.66978920735324132263, 0.52868210857343611586, 0.35651607003382906891, 0.175425242216674937, 0.037891852318801787225, 0, 0, 7.1666852050360488124e-06, 4.4542776169713856592e-05, 0.00015945105252372870864, 0.00046062008693220732759, 0.0011900631879368495612, 0.002868106285078954841, 0.0065607821717948554968, 0.014278782285967288324, 0.029185282443915855355, 0.053915675965905400513, 0.089616579303442189808, 0.13947317683937743293, 0.20621746127156848072, 0.28916251068209719577, 0.37804948423631462573, 0.44191546230800060613, 0.4167872147195200716, 0.22637517283088642861, 0, 0, 5.4104786148500394417e-10, 8.4538728357031864466e-09, 5.7985113780088147304e-08, 2.9020560343812349123e-07, 1.2408609078339073411e-06, 4.8556870182056249453e-06, 1.8016266721020254751e-05, 6.4663774098311141273e-05, 0.00022722184946421446292, 0.0007834394838982231728, 0.0024428479280998137424, 0.0067202853620942429314, 0.016883622849287124174, 0.039626432891546964354, 0.087460636122680179838, 0.179450426719582945, 0.32709043481102628714, 0.44917563313558994675, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.7023259593866311309e-08, 5.5694886140074439238e-06, 5.3443159298083143627e-05, 0.00030863478884180254799, 0.001421849073114332283, 0.0058077710675691540318, 0.022118040938587192612, 0.080697108252778662618, 0.28655734171472191374, 1, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=12
intercept=TRUE
Boundary.knots=None
knots=None
output=np.array([1, 0.25770097670924113631, 0.00075131480090157780165, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.64290814257309680801, 0.55191727963673464785, 0.16384783952351522629, 0.0025601224925549254108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.097791408043809202599, 0.4223396450334132024, 0.68523715148898289851, 0.53751029745031897455, 0.17971670556949506659, 0.0066561742803516550301, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0015994726738528394164, 0.024991760528950617698, 0.14994115610379393777, 0.44102623014868380658, 0.69378612010084461659, 0.57911832988268308053, 0.21512754628498662046, 0.013768162962239142988, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00097385288370810028057, 0.018903349908442362848, 0.12592791140580222864, 0.39992732893636862013, 0.67912381051853998315, 0.61416869876328705757, 0.2513505357806551932, 0.023605422218318485722, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00056926292385829722204, 0.014298166900596829057, 0.10543398177170558438, 0.36126140155236396989, 0.66048745076429016265, 0.64290852334776726895, 0.28801011480255123143, 0.036001264350318869234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00031466142476788003723, 0.010801736722109872915, 0.088001307706519454888, 0.32534582615335716493, 0.63869155767138829916, 0.66560703209160065885, 0.32480624491998255632, 0.050709395542809954094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00016070574853527342459, 0.0081402282805572035579, 0.073225347399450108066, 0.29228033065100789134, 0.61441387376474365656, 0.68269380491744569017, 0.36150149884261828515, 0.067452174711159121334, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.2980126610408391891e-05, 0.006111372907072773128, 0.06075244688059205922, 0.26203239546958334572, 0.58821589703491272694, 0.69467005250274826977, 0.39790980589205254825, 0.08594851807268334698, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.7434434681658694525e-05, 0.004564404070160951038, 0.05026345997459336773, 0.22896647107718723357, 0.49280704180866574671, 0.39171522957479332216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.9144147875867501007e-05, 0.0089113017089054759323, 0.10925932166656721067, 0.44968558969337457665, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.383063271446895644e-05, 0.072650662659148546041, 1, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([])
output=np.array([1, 0.9993232078902789528, 0.99830859239281100059, 0.99678795718714330309, 0.99450990091574942298, 0.99109932847208381812, 0.98599810402219045802, 0.97837913458786918142, 0.96702443008955374371, 0.95015762953345339614, 0.92522695834704149487, 0.88865464163271656872, 0.83562330741598667139, 0.7600990769655836532, 0.65556596659430688145, 0.51745533329454529436, 0.34894530919915062173, 0.17170001728344266856, 0.037087203733223626789, 0, 0, 0.00067663938125675493745, 0.0016904532697119580269, 0.0032085988210942785123, 0.0054800274275235618532, 0.0088741592009194299184, 0.013936134910759763808, 0.021463528321228261819, 0.032607690503775640933, 0.048995387512752772152, 0.072844594278869884141, 0.10699389717286836299, 0.15464119556617272888, 0.21832955541935578081, 0.29724527028257624606, 0.38124822804791375086, 0.44010197217760271826, 0.41166179703994132399, 0.22237265440082615298, 0, 0, 1.5271697506625337419e-07, 9.5415795571804482003e-07, 3.4427604254568415078e-06, 1.0065494095385778498e-05, 2.6485976820866183047e-05, 6.5657954600734260006e-05, 0.00015695450829847096968, 0.00036650624565625982386, 0.00084215780726247437687, 0.0019117237011529813161, 0.0042940168564642817658, 0.0095393459205458924072, 0.020904202366139650049, 0.044925430954654600735, 0.093631406124953353576, 0.18502397635429304601, 0.32899518168053926148, 0.4444443765651269751, 0, 0, 1.1489366970270387462e-11, 1.7952135891047479178e-10, 1.2313370007669466487e-09, 6.1626316488486427104e-09, 2.6350175916113057325e-08, 1.0311244903883365944e-07, 3.825826041044410135e-07, 1.3731610143675871737e-06, 4.8251465313854754769e-06, 1.6723672935651049261e-05, 5.7444337950753896285e-05, 0.0001961510972947611009, 0.00066716524892103249485, 0.0022633321684622557078, 0.0076650325325876428328, 0.025928742268953787475, 0.087643003996076676576, 0.29609576530082332146, 1, ]).reshape((20, 4, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([100, ])
output=np.array([1, 0.98492487882601165161, 0.96259746673448087773, 0.92974304223814019377, 0.88187654067159670923, 0.81320203136184721071, 0.71702357673990413378, 0.58746094552406258327, 0.42394246616286163087, 0.24039152271518871018, 0.078197326716827678106, 0.0025370634003338671768, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.015071708273510188089, 0.037381259008501491192, 0.070180462942842899987, 0.11790098280764790828, 0.18621717511273339074, 0.28155389351842002865, 0.40920060085307918829, 0.56848075164647782209, 0.74296035718046105067, 0.88664340493727211712, 0.92755941161894106539, 0.87470363121335126255, 0.79564729322794469635, 0.68622539174158070363, 0.5416556180357140482, 0.36526473871966497198, 0.17973006169687555378, 0.038821693326525116841, 0, 0, 3.4126433211387775282e-06, 2.1270238934697356366e-05, 7.6467258985853174243e-05, 0.00022233858750032365649, 0.00058020375050652503793, 0.0014202218581983020763, 0.003329890582419185209, 0.0075460478259266952628, 0.0165401227086783148, 0.03478495575013232366, 0.06861779430820802439, 0.12096540781627201921, 0.191329633412285538, 0.27905349779077742722, 0.37374631578125083742, 0.44360195032526716918, 0.42250875072786614473, 0.23095694223535656597, 0, 0, 2.5715731172923263021e-10, 4.0180829957692598212e-09, 2.7560031267981358852e-08, 1.3793325533914163604e-07, 5.8977491272702679876e-07, 2.3078834777630869564e-06, 8.5630404390810414166e-06, 3.0734364733920997454e-05, 0.00010799739567214114531, 0.00037431259576781883034, 0.0012857306725170922646, 0.0043281861315545133023, 0.012933767638577380737, 0.033975759516814023342, 0.080531027913042049771, 0.17293083479161802662, 0.32462175106248042367, 0.45442872954598834134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.7748388221855075202e-06, 8.9305721192384863918e-05, 0.00074535095082784331034, 0.0040670382699930507364, 0.018202476163449766294, 0.07313943651277800273, 0.27579263489213001748, 1, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([1000, ])
output=np.array([1, 0.9984992498753757495, 0.99625094117633150592, 0.99288481020069696559, 0.98784984394255781481, 0.98032935528140474624, 0.96912034075214636974, 0.95246753354617186282, 0.92784773021983435459, 0.89171926591812056273, 0.83930429776320658597, 0.76459714573223325207, 0.66106035705829924165, 0.52397052211349615103, 0.35606843795797349372, 0.17813877131971986301, 0.040531281972235988498, 3.2113319168708665608e-06, 0, 0, 0, 0.0015004113953972979083, 0.0037469425348356701772, 0.0071075541598605747329, 0.012127833209738946019, 0.019611910086607075437, 0.030734076107229148234, 0.04718451762439003494, 0.071340002591639736784, 0.10641511473995685089, 0.15646348082190494888, 0.22590626848315542574, 0.31787571113275914225, 0.42998547021895328069, 0.54537912943555955092, 0.61788880868397033641, 0.56161589860385985329, 0.31265651834613988891, 0.067535071081621433908, 0, 0, 3.3870374289331948997e-07, 2.1158906445184196164e-06, 7.6329082682329570748e-06, 2.2309178641915861703e-05, 5.8676185825556269023e-05, 0.00014535443145101360611, 0.00034710023984489360048, 0.00080922144066932952776, 0.0018549168973065219106, 0.0041951273738663188637, 0.0093691710332807192491, 0.020628857715017115404, 0.044564197866965707395, 0.093532235391030102423, 0.18697094954821646962, 0.34034142027880176506, 0.49294319032437394767, 0.3494912754584255099, 0, 0, 2.5484057919113140944e-11, 3.9818840498614283815e-10, 2.7311742697999542126e-09, 1.3669061339914938246e-08, 5.844616252249814789e-08, 2.2870917347201761224e-07, 8.4858959306208503663e-07, 3.0457478565146930827e-06, 1.0702444616158128632e-05, 3.7094041022036097191e-05, 0.00012741475133052262828, 0.00043507409392457165433, 0.0014798098005849495782, 0.0050201972154368997708, 0.017001470448093195659, 0.057511399145102344577, 0.1943970799975693331, 0.52239901147403788872, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.060574641985915035625, 1, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([10, 100, 1000, ])
output=np.array([1, 0.84242112482853226396, 0.63852451989026048906, 0.39886884859396426473, 0.16511776352451987271, 0.019111497248478225702, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.1567541293972270211, 0.35648024152864221659, 0.58396161300859317222, 0.78843465486178421209, 0.87349958752470602263, 0.78872593441389438063, 0.64620704007646878608, 0.46633671277914778841, 0.26443067498670752569, 0.086017059388510438978, 0.0027907697403672538511, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00082460534200152601353, 0.0049930443273597038822, 0.017154488011057174995, 0.046372256997114502663, 0.10706684228609344989, 0.21001709629214043717, 0.349560411044427366, 0.52164005645501687614, 0.7052885939808604121, 0.84461817796425076033, 0.85332055763972392004, 0.74044769630139117833, 0.58689461845094414993, 0.39882902037092649028, 0.19953161831184262898, 0.045398720470901786361, 3.5969836861605402089e-06, 0, 0, 0, 1.4043223919767127475e-07, 2.1942537374636142735e-06, 1.5050386385262928333e-05, 7.5324616581368114944e-05, 0.0003220729407222703883, 0.001256955617244540557, 0.0042307394405898370374, 0.012003815755055342179, 0.030163701749655900952, 0.068814016157702301291, 0.14161203135855129909, 0.25097331049689530769, 0.38604331096967514636, 0.52843933204950088722, 0.62702364005086641541, 0.58518234575989880319, 0.32861311390225123041, 0.070981847410833104339, 0, 0, 0, 0, 0, 0, 0, 1.3676720629513371727e-08, 1.8094385139768081762e-06, 1.9415010780026244647e-05, 0.00011702928277625300924, 0.00055074648953661010323, 0.0022766412613576141911, 0.0085724666641985424603, 0.026852019770306118779, 0.070978550821878275134, 0.16387889672004127273, 0.32660594492319316995, 0.49935626095003893266, 0.36224174733681202554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.5265375148376491226e-06, 0.00021005080907461989208, 0.0017530967576943525671, 0.0095658449172495805396, 0.042812988846006136412, 0.17202702816402348773, 0.50620176326643973042, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.060574641985915035625, 1, ]).reshape((20, 7, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=5
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.93886062842918460714, 0.90924840659363448392, 0.86600789475617090396, 0.80375207763469636024, 0.71607712169296244831, 0.5968951736881422665, 0.4441302646954225497, 0.26764334705075454313, 0.1016296296296296392, 0.0080000000000000019013, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.061118708396877156896, 0.090705266259710190524, 0.13388842602098646739, 0.1960165235067286571, 0.28340857382601336578, 0.40196904868840105385, 0.55338648582541094534, 0.72701318595705810566, 0.88717054427173769326, 0.96957793066711428498, 0.95879430886579075644, 0.93088336665495841071, 0.89004087164963097134, 0.83104478735029430059, 0.74753085865141255528, 0.63305354842556382788, 0.48423310979820943789, 0.30782602005074449769, 0.13273505055352541326, 0.018080241660177655966, 2.0660861734307157164e-05, 4.6319342966912629609e-05, 0.00010365288539398855667, 0.00023130996968619285063, 0.00051400448102423420497, 0.0011347651234567903995, 0.0024798322916666664696, 0.005331933984375000421, 0.011160902197265624297, 0.022290701165771482917, 0.040764443570473873901, 0.067755947834115426431, 0.10621083986053703185, 0.15942942065206799906, 0.22958192424619683347, 0.31428339352859285816, 0.39934193575081916583, 0.44659448090264364239, 0.38447678365411958046, 0.15324078379324712618, 2.3122039887776819585e-09, 7.8036884621246768168e-09, 2.6337448559670789633e-08, 8.888888888888889516e-08, 3.0000000000000003936e-07, 1.0125000000000002586e-06, 3.4171875000000000522e-06, 1.1533007812500000494e-05, 3.892390136718749976e-05, 0.0001313681671142578227, 0.00044121305262908775248, 0.0013584768001177710377, 0.0037270943566249025922, 0.0094033956626723494421, 0.022323349639243747489, 0.050359850764330853223, 0.10765352865786298464, 0.21357717575211032646, 0.36914715819588089785, 0.43210561538410807714, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.451110638240695161e-08, 2.208710808474044903e-06, 2.1194133207095667231e-05, 0.00012239633496539237658, 0.00056386746314699115034, 0.0023032072815124867565, 0.0087714257931084897019, 0.032002323294501686113, 0.11364100759647420558, 0.3965733591624671095, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=12
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.19405161102201490264, 0.050007289692374980172, 0.00014579384749963551142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.62621171786550611227, 0.59601302425037649968, 0.37359381834086596852, 0.11076923076923075873, 0.0017307692307692306051, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.17410393357590786545, 0.33496919687132054033, 0.56209998680912809377, 0.6845598495992195609, 0.49785783114523274318, 0.16619304988878053075, 0.0061552981440288964676, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0056327375365711959687, 0.019010489185927782058, 0.064160401002506278756, 0.20369706674784149314, 0.48150804971555583034, 0.70730977578155906915, 0.57961920601900573935, 0.21512754628498662046, 0.013768162962239142988, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00097385288370810028057, 0.018903349908442362848, 0.12592791140580222864, 0.39992732893636862013, 0.67912381051853998315, 0.61416869876328705757, 0.2513505357806551932, 0.023605422218318485722, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00056926292385829722204, 0.014298166900596829057, 0.10543398177170558438, 0.36126140155236396989, 0.66048745076429016265, 0.64290852334776726895, 0.28801011480255123143, 0.036001264350318869234, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00031466142476788003723, 0.010801736722109872915, 0.088001307706519454888, 0.32534582615335716493, 0.63869155767138829916, 0.66560703209160065885, 0.32480624491998255632, 0.050709395542809954094, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00016070574853527342459, 0.0081402282805572035579, 0.073225347399450108066, 0.29228033065100789134, 0.61441387376474365656, 0.68269380491744569017, 0.36150149884261828515, 0.067452174711159121334, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.2980126610408391891e-05, 0.006111372907072773128, 0.06076004701308617556, 0.2632968675132925096, 0.60214193117458769677, 0.75884556552586945877, 0.54356876952344934661, 0.23438768460872402843, 0.031926653602217504313, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.9834302187538887846e-05, 0.0032999320264517715412, 0.03634723886267338111, 0.16935876914548009253, 0.40315927025443520915, 0.49317292913076199445, 0.23502476599459576345, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.3311201208237671141e-06, 0.004343490617491435786, 0.053266330072947364049, 0.25527517275326377932, 0.49679177449105649256, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.6301491679855540114e-06, 0.017164213507250107582, 0.23625680591213024662, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([])
output=np.array([0.99900033329629622791, 0.99850074987500003765, 0.9977516870781251157, 0.9966287954511718894, 0.99494603816333004875, 0.99242545546139537826, 0.98865256904256049175, 0.98301118751693283837, 0.97458941720955349908, 0.96204716698765402327, 0.94343627795644391387, 0.91597241507140558792, 0.87578413786327058421, 0.81773305675994945041, 0.73555685971681306068, 0.62291325464817093316, 0.47647663168935677769, 0.30289524241999998821, 0.13060889169932207721, 0.017790631148623885227, 0.00099933344444444439057, 0.0014985003750000000684, 0.0022466262656250001253, 0.003367410521484375148, 0.0050454284787597655781, 0.0075553552955017096171, 0.011304291651615143086, 0.016891872202619076515, 0.02519288281652980882, 0.037464410913716578166, 0.055469506915694993809, 0.081581580145842852447, 0.11876628136094780075, 0.1701874001952980997, 0.23787846467254591953, 0.31938947503806314199, 0.40070172821401484065, 0.44437167848162473227, 0.38044436785608970464, 0.1510757732538548781, 3.3322222222222224585e-07, 7.4962500000000004377e-07, 1.6862343750000001263e-06, 3.7926035156249994121e-06, 8.5285524902343742213e-06, 1.9173024810791016186e-05, 4.3084569087982178912e-05, 9.675554396295546727e-05, 0.00021707648827975990682, 0.00048631783460495621712, 0.0010871132367784521085, 0.0024220359003474063384, 0.0053686857976677918383, 0.011806522493618208658, 0.025643231250562635581, 0.054587395598501557703, 0.11232581293261123534, 0.217309662419816918, 0.36939270915445387988, 0.42763873999331791786, 3.7037037037037035514e-11, 1.2500000000000000779e-10, 4.2187500000000000366e-10, 1.4238281249999998508e-09, 4.8054199218749994447e-09, 1.6218292236328128606e-08, 5.4736736297607425773e-08, 1.8473648500442501235e-07, 6.234856368899344564e-07, 2.1042640245035291742e-06, 7.1018910826994105393e-06, 2.3968882404110509299e-05, 8.0894978113872978626e-05, 0.00027302055113432129778, 0.00092144436007833422416, 0.003109874715264377993, 0.010495827164017276431, 0.035423416678558306003, 0.11955403129013431052, 0.40349485560420322861, ]).reshape((20, 4, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([100, ])
output=np.array([0.97029899999999991156, 0.95567162499999991354, 0.93400735937500012351, 0.9021287441406251606, 0.8556839255371094799, 0.78904911782836917311, 0.69572725948715208322, 0.57001276798105227073, 0.41135095097535856468, 0.23325165409902481883, 0.075874787916011168787, 0.0024617100802805510704, 0, 0, 0, 0, 0, 0, 0, 0, 0.029691034444444444618, 0.044305991250000002768, 0.06594240796875000532, 0.097758673769531262421, 0.1440642544409179715, 0.21038931479278566439, 0.30302618229869843214, 0.4272397443474625911, 0.58266048231123623857, 0.75392639264340943761, 0.89747740349010274308, 0.94501107412874996161, 0.90598359089303848179, 0.84593074837236126307, 0.76092088936222046502, 0.64439302204983184286, 0.49290686036830000383, 0.31333990595172406257, 0.13511264658550559137, 0.018404101188231606484, 9.9644444444444446617e-06, 2.2379999999999999178e-05, 5.0219999999999996902e-05, 0.00011253937500000000703, 0.00025167585937500001371, 0.0005610808300781249848, 0.0012449161120605468141, 0.002741945576934814565, 0.0059698621442985533997, 0.012758825336830616898, 0.02643475186140507513, 0.051808149318846051512, 0.091620856894323976505, 0.14688590543057178373, 0.21984251899359169569, 0.30818245617558825966, 0.39752224089834975462, 0.44889001546541440479, 0.38890408238266155339, 0.15565065849749706861, 1.1111111111111110654e-09, 3.7499999999999996649e-09, 1.2656249999999997938e-08, 4.2714843750000004622e-08, 1.4416259765625001146e-07, 4.8654876708984372583e-07, 1.6421020889282224026e-06, 5.5420945501327518529e-06, 1.8704569106698035598e-05, 6.3127920735105882001e-05, 0.00021305673248098232295, 0.00071906647212331535352, 0.002394473001231371169, 0.0071486127371715351211, 0.018946704087009914874, 0.045842738337222695144, 0.10249145334758576198, 0.20932413300445151805, 0.3687199031810673433, 0.43701763935524273741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0792114061605856464e-06, 3.473345989527011488e-05, 0.00028988755717800383316, 0.0015817834373569547059, 0.0070794453857644188549, 0.028445945578409920912, 0.10726336785076537317, 0.38892760095902845219, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([1000, ])
output=np.array([0.99700299900000011188, 0.99550674662500016066, 0.99326517610937503644, 0.98990913343164044225, 0.9848892569724120305, 0.97739130722329725653, 0.96621588612179176714, 0.94961298739566668559, 0.92506696964451773368, 0.88904678238644452293, 0.83678890194350608045, 0.76230564732187677812, 0.65907915850713516548, 0.52240018193475146191, 0.35500130049334505111, 0.17760488924393591503, 0.040409809679633923452, 3.2017075519046728555e-06, 0, 0, 0.0029960014444444446613, 0.0044910048749999993689, 0.0067297664531250009357, 0.010079493029296874088, 0.015085171786376951053, 0.02255122235714721729, 0.033655024381153107738, 0.050097300181899548366, 0.074283671347553537068, 0.10950057690181402847, 0.15997106401940666687, 0.23050015162429349225, 0.3250574690342031281, 0.44299931223779698275, 0.57083333883520204211, 0.6679625481063525827, 0.65410023301458430911, 0.45433806106867219432, 0.19591333754898310193, 0.026685946722935827841, 9.9944444444444452216e-07, 2.2481249999999997573e-06, 5.0561718750000005462e-06, 1.1369267578125000521e-05, 2.5556824951171875081e-05, 5.7421764678955089446e-05, 0.00012892528684616086799, 0.00028915821297883983077, 0.00064748855101794003473, 0.0014463279196678473747, 0.0032187283638391594488, 0.0071222944066175568334, 0.015620687524320137074, 0.033781444174048706752, 0.071401027591217913759, 0.14510293850391842163, 0.2740024758137300509, 0.43938848718810108451, 0.4727098830096430615, 0.21327068651931446741, 1.1111111111111111947e-10, 3.750000000000000492e-10, 1.2656250000000001661e-09, 4.2714843749999999659e-09, 1.4416259765625000816e-08, 4.8654876708984385818e-08, 1.6421020889282225085e-07, 5.5420945501327514294e-07, 1.8704569106698034751e-06, 6.3127920735105875225e-06, 2.1305673248098235006e-05, 7.190664721233152451e-05, 0.00024268493434161889522, 0.00081906165340296394756, 0.0027643330802350029977, 0.0093296241457931361474, 0.031487481492051827558, 0.10627025003567493189, 0.31773412222685926132, 0.53482276673031980962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.013642657214514538819, 0.22522060002743005125, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([10, 100, 1000, ])
output=np.array([0.72899999999999987033, 0.61412500000000003197, 0.46548437500000000577, 0.29077539062500001865, 0.12037084960937501077, 0.013932281494140625472, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.26810999999999995946, 0.37949624999999997943, 0.52058109375000005681, 0.6792815039062500837, 0.81701452880859382066, 0.86124092926025386241, 0.77303028831905795659, 0.63334751997894722653, 0.45705661219484272628, 0.2591685045544720456, 0.084305319906679110353, 0.002735233422533945441, 0, 0, 0, 0, 0, 0, 0, 0, 0.0028890000000000000749, 0.0063753749999999990983, 0.013923140625000001921, 0.029904662109375004103, 0.062484875244140626604, 0.12438889535522459906, 0.22549483803134068305, 0.36206390866050391919, 0.53039385361573909705, 0.70984283151236515774, 0.8454844390975239099, 0.85252274549222006872, 0.73970724860508996201, 0.58630772383249318835, 0.39843019135055551816, 0.19933208669353075226, 0.045353321750430879156, 3.5933867024743799741e-06, 0, 0, 9.9999999999999995475e-07, 3.3749999999999998737e-06, 1.1390625000000002433e-05, 3.8443359375000005034e-05, 0.00012974633789062501275, 0.00043789389038085933673, 0.0014748635551852823811, 0.00458723586310897085, 0.012535204497088376849, 0.030902287796947387061, 0.069803749964903766267, 0.14306169398214990673, 0.2539595816136605011, 0.39367833655080086697, 0.54755337589389629915, 0.67082414394774525501, 0.67380308481828354861, 0.47157640817504958841, 0.20334679251999171479, 0.027698480049178006435, 0, 0, 0, 0, 0, 0, 1.009441616706038712e-08, 1.3354974400350171813e-06, 1.4329692329799520979e-05, 8.6376136215493001325e-05, 0.00040649103089326774291, 0.0016803271030961133663, 0.0063296923222741757059, 0.019902020690376800299, 0.053082350626863387955, 0.12474691161612924684, 0.25803204718826688868, 0.43676084046337160238, 0.48134381599312991984, 0.21958058358824134038, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.4774589754063323979e-06, 0.00011191892632920368619, 0.00093408212868467904808, 0.0050968577425946322637, 0.022811546243018676616, 0.091659157974876420694, 0.30166673427236384564, 0.52750033633515069909, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.013642657214514538819, 0.22522060002743005125, ]).reshape((20, 7, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=5
intercept=FALSE
Boundary.knots=None
knots=None
output=np.array([0, 0.11681123728381428983, 0.2730259078882545376, 0.46749900909245512004, 0.67935641673089897097, 0.85057627482867270707, 0.88934886597560480759, 0.80216081429413377268, 0.67739211125208298458, 0.51560190205519351725, 0.32469390916595280983, 0.13698024292938629221, 0.017122530366173265709, 0, 0, 0, 0, 0, 0, 0, 0, 0.0003496520800200754106, 0.0021367360720303471555, 0.0074505054668944550172, 0.02064467788236522966, 0.049822794327671247883, 0.10672495197833482827, 0.1969631195446764349, 0.31985257188474064405, 0.47686971233712693863, 0.65666375567302859295, 0.82030294962505467815, 0.89243231477003071017, 0.82865930832017831165, 0.7146974084023058893, 0.56412932414843675044, 0.38041985226043306678, 0.187187199501233692, 0.04043243509226646798, 0, 0, 2.6689299275350298406e-08, 4.1702030117734841467e-07, 2.86034224577543261e-06, 1.4315525026353663709e-05, 6.1210311482175452555e-05, 0.00023952573005394856335, 0.00087599557622739540694, 0.0027528938137588820738, 0.0075078612376215788857, 0.018529647319498028513, 0.042210845625950982329, 0.088408426246782453872, 0.16366728062036642322, 0.25992689327612594763, 0.36501727743515482993, 0.44566378087513086603, 0.43199704724253940036, 0.23880246889964723556, 0, 0, 0, 0, 0, 0, 0, 0, 7.058496255109760934e-08, 2.4230494175743949962e-06, 2.0524370058046372483e-05, 0.00011268784152075793045, 0.00050596181960801099326, 0.0020367286170134709343, 0.0076722266846277449928, 0.025178647959627011715, 0.068676202883725015469, 0.1606491966704423624, 0.31804567985939574681, 0.46051790249468016469, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.1843748275951593331e-06, 0.00019705036194114395882, 0.0021771955326834735445, 0.013267170193993682234, 0.062770073396831202461, 0.26024719351340613871, 1, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=12
intercept=FALSE
Boundary.knots=None
knots=None
output=np.array([0, 0.67103518571234044288, 0.42848228882111855098, 0.069535818471346141911, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.13629558602559715963, 0.52330141887680614587, 0.67103578567914412556, 0.33835295499291540011, 0.045464904980911415022, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0030869584374568800715, 0.048215269973619155619, 0.25524093849303608472, 0.60346220790852211913, 0.66476640009174547963, 0.3023720875414114273, 0.032137228723967029009, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0223284561703245952e-06, 0.0041874573564736911752, 0.058175656367661768287, 0.28413057856289342107, 0.62749346465948352414, 0.64458417746071206, 0.26642650281928137446, 0.020972348618427394396, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.1807309007930243297e-06, 0.0056381163644495775958, 0.070099504751495456123, 0.31573089553161143295, 0.64918292160807222757, 0.61885031117979483195, 0.23077208437307619726, 0.012169621636861570682, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.494304760976545528e-05, 0.0075476982837094025794, 0.084296696676371143941, 0.3501161628650154567, 0.66789290688121760731, 0.58719180485958433202, 0.19572881144127046715, 0.0058504368174458563218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.3878896275372805593e-05, 0.010061177336762315224, 0.10112604997332759471, 0.38726521207131708868, 0.68285613364861541541, 0.54940228374944388712, 0.16169301614535666611, 0.001987124003607085819, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00020895877237870428959, 0.013373361432237074534, 0.12100109812882933746, 0.42699888243551975542, 0.69315383369930994029, 0.50558220978145163027, 0.12915009496447266146, 0.00028659920802505926699, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00041395678128490346389, 0.017748396997590374508, 0.14439460468269080251, 0.46888258402048205165, 0.69769409523153691488, 0.45636616933565676835, 0.098685730485585029803, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00075854547264270536476, 0.023548082194459314664, 0.16992724106945886198, 0.46662458342302287617, 0.42010939261246582621, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0032285687345316718341, 0.076722648033295162695, 0.42847050190194924113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.052734374999999840405, 1, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([])
output=np.array([0, 0.00067663938125675493745, 0.0016904532697119580269, 0.0032085988210942785123, 0.0054800274275235618532, 0.0088741592009194299184, 0.013936134910759763808, 0.021463528321228261819, 0.032607690503775640933, 0.048995387512752772152, 0.072844594278869884141, 0.10699389717286836299, 0.15464119556617272888, 0.21832955541935578081, 0.29724527028257624606, 0.38124822804791375086, 0.44010197217760271826, 0.41166179703994132399, 0.22237265440082615298, 0, 0, 1.5271697506625337419e-07, 9.5415795571804482003e-07, 3.4427604254568415078e-06, 1.0065494095385778498e-05, 2.6485976820866183047e-05, 6.5657954600734260006e-05, 0.00015695450829847096968, 0.00036650624565625982386, 0.00084215780726247437687, 0.0019117237011529813161, 0.0042940168564642817658, 0.0095393459205458924072, 0.020904202366139650049, 0.044925430954654600735, 0.093631406124953353576, 0.18502397635429304601, 0.32899518168053926148, 0.4444443765651269751, 0, 0, 1.1489366970270387462e-11, 1.7952135891047479178e-10, 1.2313370007669466487e-09, 6.1626316488486427104e-09, 2.6350175916113057325e-08, 1.0311244903883365944e-07, 3.825826041044410135e-07, 1.3731610143675871737e-06, 4.8251465313854754769e-06, 1.6723672935651049261e-05, 5.7444337950753896285e-05, 0.0001961510972947611009, 0.00066716524892103249485, 0.0022633321684622557078, 0.0076650325325876428328, 0.025928742268953787475, 0.087643003996076676576, 0.29609576530082332146, 1, ]).reshape((20, 3, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([100, ])
output=np.array([0, 0.015071708273510188089, 0.037381259008501491192, 0.070180462942842899987, 0.11790098280764790828, 0.18621717511273339074, 0.28155389351842002865, 0.40920060085307918829, 0.56848075164647782209, 0.74296035718046105067, 0.88664340493727211712, 0.92755941161894106539, 0.87470363121335126255, 0.79564729322794469635, 0.68622539174158070363, 0.5416556180357140482, 0.36526473871966497198, 0.17973006169687555378, 0.038821693326525116841, 0, 0, 3.4126433211387775282e-06, 2.1270238934697356366e-05, 7.6467258985853174243e-05, 0.00022233858750032365649, 0.00058020375050652503793, 0.0014202218581983020763, 0.003329890582419185209, 0.0075460478259266952628, 0.0165401227086783148, 0.03478495575013232366, 0.06861779430820802439, 0.12096540781627201921, 0.191329633412285538, 0.27905349779077742722, 0.37374631578125083742, 0.44360195032526716918, 0.42250875072786614473, 0.23095694223535656597, 0, 0, 2.5715731172923263021e-10, 4.0180829957692598212e-09, 2.7560031267981358852e-08, 1.3793325533914163604e-07, 5.8977491272702679876e-07, 2.3078834777630869564e-06, 8.5630404390810414166e-06, 3.0734364733920997454e-05, 0.00010799739567214114531, 0.00037431259576781883034, 0.0012857306725170922646, 0.0043281861315545133023, 0.012933767638577380737, 0.033975759516814023342, 0.080531027913042049771, 0.17293083479161802662, 0.32462175106248042367, 0.45442872954598834134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.7748388221855075202e-06, 8.9305721192384863918e-05, 0.00074535095082784331034, 0.0040670382699930507364, 0.018202476163449766294, 0.07313943651277800273, 0.27579263489213001748, 1, ]).reshape((20, 4, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([1000, ])
output=np.array([0, 0.0015004113953972979083, 0.0037469425348356701772, 0.0071075541598605747329, 0.012127833209738946019, 0.019611910086607075437, 0.030734076107229148234, 0.04718451762439003494, 0.071340002591639736784, 0.10641511473995685089, 0.15646348082190494888, 0.22590626848315542574, 0.31787571113275914225, 0.42998547021895328069, 0.54537912943555955092, 0.61788880868397033641, 0.56161589860385985329, 0.31265651834613988891, 0.067535071081621433908, 0, 0, 3.3870374289331948997e-07, 2.1158906445184196164e-06, 7.6329082682329570748e-06, 2.2309178641915861703e-05, 5.8676185825556269023e-05, 0.00014535443145101360611, 0.00034710023984489360048, 0.00080922144066932952776, 0.0018549168973065219106, 0.0041951273738663188637, 0.0093691710332807192491, 0.020628857715017115404, 0.044564197866965707395, 0.093532235391030102423, 0.18697094954821646962, 0.34034142027880176506, 0.49294319032437394767, 0.3494912754584255099, 0, 0, 2.5484057919113140944e-11, 3.9818840498614283815e-10, 2.7311742697999542126e-09, 1.3669061339914938246e-08, 5.844616252249814789e-08, 2.2870917347201761224e-07, 8.4858959306208503663e-07, 3.0457478565146930827e-06, 1.0702444616158128632e-05, 3.7094041022036097191e-05, 0.00012741475133052262828, 0.00043507409392457165433, 0.0014798098005849495782, 0.0050201972154368997708, 0.017001470448093195659, 0.057511399145102344577, 0.1943970799975693331, 0.52239901147403788872, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.060574641985915035625, 1, ]).reshape((20, 4, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([10, 100, 1000, ])
output=np.array([0, 0.1567541293972270211, 0.35648024152864221659, 0.58396161300859317222, 0.78843465486178421209, 0.87349958752470602263, 0.78872593441389438063, 0.64620704007646878608, 0.46633671277914778841, 0.26443067498670752569, 0.086017059388510438978, 0.0027907697403672538511, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00082460534200152601353, 0.0049930443273597038822, 0.017154488011057174995, 0.046372256997114502663, 0.10706684228609344989, 0.21001709629214043717, 0.349560411044427366, 0.52164005645501687614, 0.7052885939808604121, 0.84461817796425076033, 0.85332055763972392004, 0.74044769630139117833, 0.58689461845094414993, 0.39882902037092649028, 0.19953161831184262898, 0.045398720470901786361, 3.5969836861605402089e-06, 0, 0, 0, 1.4043223919767127475e-07, 2.1942537374636142735e-06, 1.5050386385262928333e-05, 7.5324616581368114944e-05, 0.0003220729407222703883, 0.001256955617244540557, 0.0042307394405898370374, 0.012003815755055342179, 0.030163701749655900952, 0.068814016157702301291, 0.14161203135855129909, 0.25097331049689530769, 0.38604331096967514636, 0.52843933204950088722, 0.62702364005086641541, 0.58518234575989880319, 0.32861311390225123041, 0.070981847410833104339, 0, 0, 0, 0, 0, 0, 0, 1.3676720629513371727e-08, 1.8094385139768081762e-06, 1.9415010780026244647e-05, 0.00011702928277625300924, 0.00055074648953661010323, 0.0022766412613576141911, 0.0085724666641985424603, 0.026852019770306118779, 0.070978550821878275134, 0.16387889672004127273, 0.32660594492319316995, 0.49935626095003893266, 0.36224174733681202554, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.5265375148376491226e-06, 0.00021005080907461989208, 0.0017530967576943525671, 0.0095658449172495805396, 0.042812988846006136412, 0.17202702816402348773, 0.50620176326643973042, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.060574641985915035625, 1, ]).reshape((20, 6, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=5
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.20791834248678603414, 0.29902312242189532654, 0.42058546782444744538, 0.57127689681172544311, 0.73389736255977222612, 0.86146730066325027941, 0.87986332643537079612, 0.79291382076038952054, 0.66958340212426392668, 0.50965824665676062732, 0.32095096582466564605, 0.13540118870728076739, 0.016925148588410078576, 0, 0, 0, 0, 0, 0, 0, 0.0012695557913002517882, 0.0028166847710175994465, 0.0062031625467877079053, 0.013503589345765960872, 0.028852424480264381862, 0.059752006107076843788, 0.11700693595529969293, 0.20637160010579103098, 0.32827217823508614281, 0.48461478028531612683, 0.66503213888472001436, 0.83266225254172265835, 0.91555098377957688793, 0.87163430177910428132, 0.78404142346700400612, 0.66397286412208100792, 0.507883804798330174, 0.32286071959112722096, 0.1392180359854287286, 0.018963308663199267973, 1.4499417527118528165e-07, 4.893553415402503421e-07, 1.6515742776983449575e-06, 5.5740631872319126435e-06, 1.8812463256907710572e-05, 6.3492063492063516403e-05, 0.000214285714285714356, 0.0007145407126011744112, 0.0021431007122621795712, 0.0057158011131347528228, 0.013955556387630695808, 0.03166115066306447734, 0.066415223634956249699, 0.12418853438999710725, 0.20209136094044644061, 0.29685688827833944803, 0.39377505733963352741, 0.45246903544196670488, 0.39638281649040951748, 0.1597891894713379668, 0, 0, 0, 0, 0, 0, 0, 3.8421218399486777976e-08, 1.3189283878698809637e-06, 1.117194478839918966e-05, 6.133890298378388427e-05, 0.00027540808793216300541, 0.0011086439970568826713, 0.0041767162702452207551, 0.013792752688869525796, 0.038347512235655537016, 0.093327637726685397368, 0.20095020769675242533, 0.36605493991801690834, 0.4433598240831516657, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.4756065322375969297e-07, 7.4462903680102773569e-05, 0.0008227353639237797861, 0.0050135001353509671407, 0.023720037270153770254, 0.09834420760614477619, 0.37788767778231108219, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=12
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.61574942641905272556, 0.5337910278271761344, 0.26630652260904380535, 0.04321728691476599965, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.21709357831448763965, 0.40428360185244682778, 0.62558529044677246844, 0.63046184489143586305, 0.30534794185536534572, 0.041029980550522253402, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0094918061936686368846, 0.032034845903631647968, 0.10810716461572758562, 0.32213341083732455195, 0.63646722104607222903, 0.66920132452213465513, 0.3023720875414114273, 0.032137228723967029009, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0223284561703245952e-06, 0.0041874573564736911752, 0.058175656367661768287, 0.28413057856289342107, 0.62749346465948352414, 0.64458417746071206, 0.26642650281928137446, 0.020972348618427394396, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.1807309007930243297e-06, 0.0056381163644495775958, 0.070099504751495456123, 0.31573089553161143295, 0.64918292160807222757, 0.61885031117979483195, 0.23077208437307619726, 0.012169621636861570682, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.494304760976545528e-05, 0.0075476982837094025794, 0.084296696676371143941, 0.3501161628650154567, 0.66789290688121760731, 0.58719180485958433202, 0.19572881144127046715, 0.0058504368174458563218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9.3878896275372805593e-05, 0.010061177336762315224, 0.10112604997332759471, 0.38726521207131708868, 0.68285613364861541541, 0.54940228374944388712, 0.16169301614535666611, 0.001987124003607085819, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00020895877237870428959, 0.013373361432237074534, 0.12100109812882933746, 0.42699888243551975542, 0.69315383369930994029, 0.50558220978145163027, 0.12915009496447266146, 0.00028659920802505926699, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00041395678128490346389, 0.017748396997590374508, 0.14460859026960704021, 0.4755254951851585199, 0.74591378938143138022, 0.59473172632230031365, 0.2566313326020329133, 0.03495652799821041129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00054455988572647427617, 0.016905171029782832537, 0.12340338564379622899, 0.3685583814319431939, 0.50219016290765505772, 0.24887848966988834754, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0015327300102998579149, 0.03642329303773143151, 0.22969990963832651043, 0.49849681328683803638, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.011478594851985721506, 0.21766816904506322561, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([])
output=np.array([0.00099933344444444439057, 0.0014985003750000000684, 0.0022466262656250001253, 0.003367410521484375148, 0.0050454284787597655781, 0.0075553552955017096171, 0.011304291651615143086, 0.016891872202619076515, 0.02519288281652980882, 0.037464410913716578166, 0.055469506915694993809, 0.081581580145842852447, 0.11876628136094780075, 0.1701874001952980997, 0.23787846467254591953, 0.31938947503806314199, 0.40070172821401484065, 0.44437167848162473227, 0.38044436785608970464, 0.1510757732538548781, 3.3322222222222224585e-07, 7.4962500000000004377e-07, 1.6862343750000001263e-06, 3.7926035156249994121e-06, 8.5285524902343742213e-06, 1.9173024810791016186e-05, 4.3084569087982178912e-05, 9.675554396295546727e-05, 0.00021707648827975990682, 0.00048631783460495621712, 0.0010871132367784521085, 0.0024220359003474063384, 0.0053686857976677918383, 0.011806522493618208658, 0.025643231250562635581, 0.054587395598501557703, 0.11232581293261123534, 0.217309662419816918, 0.36939270915445387988, 0.42763873999331791786, 3.7037037037037035514e-11, 1.2500000000000000779e-10, 4.2187500000000000366e-10, 1.4238281249999998508e-09, 4.8054199218749994447e-09, 1.6218292236328128606e-08, 5.4736736297607425773e-08, 1.8473648500442501235e-07, 6.234856368899344564e-07, 2.1042640245035291742e-06, 7.1018910826994105393e-06, 2.3968882404110509299e-05, 8.0894978113872978626e-05, 0.00027302055113432129778, 0.00092144436007833422416, 0.003109874715264377993, 0.010495827164017276431, 0.035423416678558306003, 0.11955403129013431052, 0.40349485560420322861, ]).reshape((20, 3, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([100, ])
output=np.array([0.029691034444444444618, 0.044305991250000002768, 0.06594240796875000532, 0.097758673769531262421, 0.1440642544409179715, 0.21038931479278566439, 0.30302618229869843214, 0.4272397443474625911, 0.58266048231123623857, 0.75392639264340943761, 0.89747740349010274308, 0.94501107412874996161, 0.90598359089303848179, 0.84593074837236126307, 0.76092088936222046502, 0.64439302204983184286, 0.49290686036830000383, 0.31333990595172406257, 0.13511264658550559137, 0.018404101188231606484, 9.9644444444444446617e-06, 2.2379999999999999178e-05, 5.0219999999999996902e-05, 0.00011253937500000000703, 0.00025167585937500001371, 0.0005610808300781249848, 0.0012449161120605468141, 0.002741945576934814565, 0.0059698621442985533997, 0.012758825336830616898, 0.02643475186140507513, 0.051808149318846051512, 0.091620856894323976505, 0.14688590543057178373, 0.21984251899359169569, 0.30818245617558825966, 0.39752224089834975462, 0.44889001546541440479, 0.38890408238266155339, 0.15565065849749706861, 1.1111111111111110654e-09, 3.7499999999999996649e-09, 1.2656249999999997938e-08, 4.2714843750000004622e-08, 1.4416259765625001146e-07, 4.8654876708984372583e-07, 1.6421020889282224026e-06, 5.5420945501327518529e-06, 1.8704569106698035598e-05, 6.3127920735105882001e-05, 0.00021305673248098232295, 0.00071906647212331535352, 0.002394473001231371169, 0.0071486127371715351211, 0.018946704087009914874, 0.045842738337222695144, 0.10249145334758576198, 0.20932413300445151805, 0.3687199031810673433, 0.43701763935524273741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0792114061605856464e-06, 3.473345989527011488e-05, 0.00028988755717800383316, 0.0015817834373569547059, 0.0070794453857644188549, 0.028445945578409920912, 0.10726336785076537317, 0.38892760095902845219, ]).reshape((20, 4, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([1000, ])
output=np.array([0.0029960014444444446613, 0.0044910048749999993689, 0.0067297664531250009357, 0.010079493029296874088, 0.015085171786376951053, 0.02255122235714721729, 0.033655024381153107738, 0.050097300181899548366, 0.074283671347553537068, 0.10950057690181402847, 0.15997106401940666687, 0.23050015162429349225, 0.3250574690342031281, 0.44299931223779698275, 0.57083333883520204211, 0.6679625481063525827, 0.65410023301458430911, 0.45433806106867219432, 0.19591333754898310193, 0.026685946722935827841, 9.9944444444444452216e-07, 2.2481249999999997573e-06, 5.0561718750000005462e-06, 1.1369267578125000521e-05, 2.5556824951171875081e-05, 5.7421764678955089446e-05, 0.00012892528684616086799, 0.00028915821297883983077, 0.00064748855101794003473, 0.0014463279196678473747, 0.0032187283638391594488, 0.0071222944066175568334, 0.015620687524320137074, 0.033781444174048706752, 0.071401027591217913759, 0.14510293850391842163, 0.2740024758137300509, 0.43938848718810108451, 0.4727098830096430615, 0.21327068651931446741, 1.1111111111111111947e-10, 3.750000000000000492e-10, 1.2656250000000001661e-09, 4.2714843749999999659e-09, 1.4416259765625000816e-08, 4.8654876708984385818e-08, 1.6421020889282225085e-07, 5.5420945501327514294e-07, 1.8704569106698034751e-06, 6.3127920735105875225e-06, 2.1305673248098235006e-05, 7.190664721233152451e-05, 0.00024268493434161889522, 0.00081906165340296394756, 0.0027643330802350029977, 0.0093296241457931361474, 0.031487481492051827558, 0.10627025003567493189, 0.31773412222685926132, 0.53482276673031980962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.013642657214514538819, 0.22522060002743005125, ]).reshape((20, 4, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=3
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([10, 100, 1000, ])
output=np.array([0.26810999999999995946, 0.37949624999999997943, 0.52058109375000005681, 0.6792815039062500837, 0.81701452880859382066, 0.86124092926025386241, 0.77303028831905795659, 0.63334751997894722653, 0.45705661219484272628, 0.2591685045544720456, 0.084305319906679110353, 0.002735233422533945441, 0, 0, 0, 0, 0, 0, 0, 0, 0.0028890000000000000749, 0.0063753749999999990983, 0.013923140625000001921, 0.029904662109375004103, 0.062484875244140626604, 0.12438889535522459906, 0.22549483803134068305, 0.36206390866050391919, 0.53039385361573909705, 0.70984283151236515774, 0.8454844390975239099, 0.85252274549222006872, 0.73970724860508996201, 0.58630772383249318835, 0.39843019135055551816, 0.19933208669353075226, 0.045353321750430879156, 3.5933867024743799741e-06, 0, 0, 9.9999999999999995475e-07, 3.3749999999999998737e-06, 1.1390625000000002433e-05, 3.8443359375000005034e-05, 0.00012974633789062501275, 0.00043789389038085933673, 0.0014748635551852823811, 0.00458723586310897085, 0.012535204497088376849, 0.030902287796947387061, 0.069803749964903766267, 0.14306169398214990673, 0.2539595816136605011, 0.39367833655080086697, 0.54755337589389629915, 0.67082414394774525501, 0.67380308481828354861, 0.47157640817504958841, 0.20334679251999171479, 0.027698480049178006435, 0, 0, 0, 0, 0, 0, 1.009441616706038712e-08, 1.3354974400350171813e-06, 1.4329692329799520979e-05, 8.6376136215493001325e-05, 0.00040649103089326774291, 0.0016803271030961133663, 0.0063296923222741757059, 0.019902020690376800299, 0.053082350626863387955, 0.12474691161612924684, 0.25803204718826688868, 0.43676084046337160238, 0.48134381599312991984, 0.21958058358824134038, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.4774589754063323979e-06, 0.00011191892632920368619, 0.00093408212868467904808, 0.0050968577425946322637, 0.022811546243018676616, 0.091659157974876420694, 0.30166673427236384564, 0.52750033633515069909, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.013642657214514538819, 0.22522060002743005125, ]).reshape((20, 6, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=12
intercept=TRUE
Boundary.knots=None
knots=None
output=np.array([1, 0.24780328615799376846, 0.0091743090758214639741, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.64369896942547122354, 0.57211971408849160436, 0.24237606578683060232, 0.044144491052264721309, 0.00040499520490831021667, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.10612816657504857421, 0.39035389287158045457, 0.63115822592338399755, 0.61942792972003923868, 0.3746409686363950664, 0.1299270153679348283, 0.01456850160548600788, 2.4933115730197022697e-06, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0023615475345642161081, 0.028071577965966144214, 0.12345862383415923125, 0.3183634697319876472, 0.55248624494736087165, 0.66157907323456133231, 0.54063561857561015511, 0.27886806629206489783, 0.077562585629719432712, 0.0043678218067639239253, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8.026857613403984366e-06, 0.00028016915158263086755, 0.0029987443674941152358, 0.017947370921348515527, 0.071410333320899577192, 0.20176799613356366514, 0.41383987857855386583, 0.6154467549253210823, 0.65231295225138286042, 0.47044608242977714596, 0.21195012900662815736, 0.045727490940629646199, 0.00095767216067723433693, 0, 0, 0, 0, 0, 0, 0, 3.4493087511455702552e-09, 3.3684655772905958113e-07, 8.3400865772486489595e-06, 0.00011672374464387017944, 0.001056583561129178974, 0.0067076933166381781659, 0.030729882600253097258, 0.10383387100350169319, 0.2594160581947105304, 0.48008955662192837055, 0.64645432100283450882, 0.61990700734803594329, 0.39505102004440167951, 0.15152949559297854143, 0.022327498493473717928, 5.4255821339140527268e-05, 0, 0, 0, 0, 0, 0, 1.5550084505986725427e-12, 1.4829716211306275165e-08, 8.7432930709587782537e-07, 1.8221855962693547294e-05, 0.00022606146610042746276, 0.0018465551075490084856, 0.010668371345813800616, 0.044660809207147045274, 0.13839012473302086947, 0.31749140013695392737, 0.53873603574199457888, 0.65962064818889976081, 0.56521993555148342114, 0.310506638922921685, 0.095251921793756827439, 0.0074067894386825334357, 0, 0, 0, 0, 0, 0, 0, 9.1339471742565674011e-11, 5.7173996413972389293e-08, 2.2593599905185296879e-06, 4.0028808856421427135e-05, 0.00043513707847336695594, 0.0031885139595084076476, 0.016618589891805510966, 0.062812605113451408512, 0.1727378974109962384, 0.33696595086343833492, 0.43637044859132123609, 0.31354081400102012944, 0.068636818178064884499, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.7695168114557225724e-09, 5.9285591015796308443e-07, 1.6911296921410870622e-05, 0.00025529307410796154469, 0.0024263360322963763681, 0.015741731604329466804, 0.070644470707028478307, 0.21311252564833091383, 0.3762972750744253414, 0.24384934178453185338, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0868704401501409189e-12, 2.1860846716771802376e-07, 1.6330907178590211063e-05, 0.00037021035039809161273, 0.0048110469704648085865, 0.038471273482950811562, 0.18655513337806783891, 0.39552144655799137407, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.6852398034939565224e-08, 3.1097414111410212796e-05, 0.0014848575331362907741, 0.028249998152729726558, 0.25456576340175340878, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00010485760000000057303, 0.030019840638976030833, 1, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([])
output=np.array([1, 0.99887226764047176708, 0.99718257698671308731, 0.99465232914808876519, 0.99086659019744105503, 0.98520960330607720845, 0.9767725953351565904, 0.96422555375161622671, 0.94564705949867622348, 0.91831730137969458383, 0.87851085191767241955, 0.82140061321171964348, 0.74134024099742168445, 0.63306797132011838336, 0.49472136419952228437, 0.33351966492579832035, 0.17295333506564730675, 0.053041675157364839843, 0.0041245206602366912829, 0, 0, 0.0011272234177996938269, 0.0028142442804627715303, 0.0053362072774053633667, 0.0090999196124958610377, 0.014702372431044495246, 0.023009568696928141274, 0.035255048800207078319, 0.053144756365466998271, 0.078922539871719557536, 0.11527760116980134697, 0.16482753559964863355, 0.22865514515079901625, 0.30306893078627178406, 0.37385911082806172478, 0.40954833229285447782, 0.36355794566479110452, 0.2119513600790149388, 0.041217298467029310494, 0, 0, 5.0882687398560041373e-07, 3.1769391294621280289e-06, 1.1451280924188917779e-05, 3.3428733100138665235e-05, 8.7761935886847988286e-05, 0.00021681208262686383053, 0.00051561316170019592855, 0.0011946804469067555735, 0.0027131220507094727483, 0.0060506596144855023106, 0.013230141811374926397, 0.028210083582453096551, 0.058035333309565624582, 0.11300958063559926603, 0.20116335451605230067, 0.30568795867586873172, 0.33877798094471850421, 0.16475763686175207146, 0, 0, 1.1484182443411814437e-10, 1.7931887260774516498e-09, 1.2286988490864389756e-08, 6.1400553206305291637e-08, 2.6193586874264870438e-07, 1.0214767558696939837e-06, 3.7704802796487805225e-06, 1.3428054504627923681e-05, 4.6634530984498030184e-05, 0.00015879269432593164958, 0.00053096908751411363639, 0.00174019442073816092, 0.0055566565395111476577, 0.017080184681265514479, 0.049404053208565550104, 0.12851476524402274948, 0.27074730808567093465, 0.32929231067852438031, 0, 0, 1.2959854631941128047e-14, 5.06072933143619642e-13, 6.5918427455453850986e-12, 5.6389033989814727996e-11, 3.9088927700060016774e-10, 2.406265255469648607e-09, 1.378603437152614992e-08, 7.5464802427343835863e-08, 4.0078909822273000137e-07, 2.0836670196190539855e-06, 1.0654767572225721294e-05, 5.3673655611779830157e-05, 0.00026601408260545672708, 0.0012907432586925205419, 0.0060666130749972404027, 0.027014549341864656923, 0.10818900424286860551, 0.32906949849914446382, 0, 0, 5.8500579526198340832e-19, 5.7129472193553077444e-17, 1.4145822896898457119e-15, 2.0714622186680568112e-14, 2.3333104795078233906e-13, 2.2673496764008742317e-12, 2.0162390952550372537e-11, 1.6964293385718098169e-10, 1.3777936465799164788e-09, 1.0936695210263550545e-08, 8.5522170452256458058e-08, 6.6219297623306537514e-07, 5.0939619277349284707e-06, 3.9016396858695978102e-05, 0.00029798198173219142288, 0.00227144600780565184, 0.017292671490362138825, 0.13153873483331313121, 1, ]).reshape((20, 6, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([100, ])
output=np.array([1, 0.97500126574733914087, 0.93844290960000364965, 0.88566924020111725824, 0.81098530154789516544, 0.70848516022771668155, 0.57441053105822748037, 0.41206430922482584212, 0.2392461015407435776, 0.092939125284492898893, 0.014299466806025581608, 4.7193863677059842883e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.024987397851780435848, 0.061486796626462968118, 0.1140799960900419352, 0.1882939537142028219, 0.28966625638367565765, 0.42117968175620157378, 0.57798465087862005429, 0.73943782746930653005, 0.86397935690336380432, 0.90462871246453147034, 0.85976637084822715718, 0.77601114642224255924, 0.66267521310641597232, 0.51785842958629058064, 0.34911766989097126057, 0.18104199448456753663, 0.055522321426452851678, 0.0043174157141209739547, 0, 0, 1.1333836374491041766e-05, 7.0253868335539153452e-05, 0.00025049169236306427153, 0.00071939598049462782393, 0.0018428967293926601032, 0.0043879998552115674279, 0.0098727402942039785283, 0.021048289930652556295, 0.042207103219050953746, 0.078361329513138483494, 0.1323267251748408424, 0.20305646979998087653, 0.28625091024051202426, 0.36712456913477936604, 0.41237454577086030127, 0.37209382828918063923, 0.21926721332006066101, 0.042943027456719351509, 0, 0, 2.5642163564224008165e-09, 3.9893896948733327568e-08, 2.7186958324120860093e-07, 1.3475047468249140318e-06, 5.6780166980285093331e-06, 2.1734507210374597486e-05, 7.8000238621073664019e-05, 0.00026616938183067457386, 0.00086607259863206911138, 0.0026688434677071591963, 0.0076602480569788727188, 0.020032890189762748295, 0.047362176442216685768, 0.10112516340575794516, 0.19128545657973258787, 0.30258229747242648688, 0.34436724521558481626, 0.17045464690987927048, 0, 0, 2.8979008849349871451e-13, 1.1299684644739179399e-11, 1.4686295227238142049e-10, 1.2521970298985838129e-09, 8.6372943219355255736e-09, 5.2772400925513216102e-08, 2.9891245041530529781e-07, 1.607880484564848822e-06, 8.3111564072833673416e-06, 4.1402961295506068027e-05, 0.00019754788193526286806, 0.00088468396831814228969, 0.0036015013400145573161, 0.013149579882492583999, 0.042768561910798960635, 0.12037399719492403172, 0.26730426029673903798, 0.33672081019045274619, 0, 0, 1.309371682920953332e-17, 1.2786832841024935257e-15, 3.1661464010829005262e-14, 4.6363882090260461786e-13, 5.2224622287067364928e-12, 5.0748274386406234902e-11, 4.5127867086219768747e-10, 3.7969821085404869e-09, 3.0838053235562167103e-08, 2.44787301750460405e-07, 1.9141743408195724709e-06, 1.4809071760067401229e-05, 0.00011002044182894339316, 0.0007361307065949982114, 0.0043501410333869746858, 0.022648326644076191561, 0.10074752233050462968, 0.32871166293410319925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.4793570769921117731e-10, 1.7842901170031562027e-07, 6.1272840845677704696e-06, 0.00010362481425005589904, 0.0012595559148250148798, 0.012791437410658173385, 0.11685243679472454015, 1, ]).reshape((20, 7, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([1000, ])
output=np.array([1, 0.9975000012515636838, 0.99375938046297329631, 0.9881694981371595965, 0.97983186568317970355, 0.96743102855471574397, 0.94906548971852167096, 0.92204114544049331492, 0.88266217520230549898, 0.82612719043156179755, 0.74679100501527517775, 0.63932416597329111418, 0.50165120112244221406, 0.34054780971703357828, 0.17887753393501265586, 0.056398049910775530091, 0.0047824514208348522723, 6.9899702309353164834e-10, 0, 0, 0, 0.0024988701975066367374, 0.0062335737743966241481, 0.011805108206123089004, 0.020094017057672219906, 0.032374436158623724757, 0.050454096263104174225, 0.076816980711751164934, 0.11469423978741608017, 0.16787638509201657788, 0.23985925947403505254, 0.33155764168655321722, 0.4364690436594241274, 0.53267348082579712987, 0.57514542430970139186, 0.50463302930351572329, 0.30623588293439429897, 0.096587849561305616497, 0.0075106712808647091081, 0, 0, 1.1282962103271197581e-06, 7.0417854820895302466e-06, 2.536640667453833115e-05, 7.3981096505374880712e-05, 0.00019395448667024030114, 0.00047814948330325205252, 0.0011335173229715391711, 0.0026138368581788955919, 0.0058931764589364287604, 0.012998501527337540801, 0.027945386445582046098, 0.058044007669012950834, 0.1145680954987259581, 0.2086069927114643785, 0.33148565972609811414, 0.41061823564260202524, 0.3066625410662744966, 0.068889778730265194273, 0, 0, 2.5469057156714312774e-10, 3.976025713192789869e-09, 2.7235422387831816846e-08, 1.3603757883811768205e-07, 5.7993308882550318022e-07, 2.2591989366454093128e-06, 8.3259563410340815473e-06, 2.9580849000601624262e-05, 0.00010235971125482330811, 0.00034661758311907198181, 0.001149214388416119545, 0.0037170166582407206807, 0.011623043605661884797, 0.034525979049628140183, 0.094171296281739258482, 0.21954225794050497012, 0.36514412761590397949, 0.24346326935859208263, 0, 0, 2.8744101198050118059e-14, 1.1223436953991581798e-12, 1.4617253735361878953e-11, 1.2501806315302350017e-10, 8.6638386346496238246e-10, 5.3311050540562872207e-09, 3.0523721489433262118e-08, 1.6692682142232341016e-07, 8.8525020719146049419e-07, 4.5921420319874677743e-06, 2.3401813204582281116e-05, 0.0001172621098580812533, 0.00057627166059595505247, 0.0027575294458836020588, 0.012651024092369157759, 0.053782977897194939043, 0.19324936944781076487, 0.3997562713358867037, 0, 0, 1.2975755416333770318e-18, 1.2671636148763449243e-16, 3.1376225596317031531e-15, 4.5946189458816680778e-14, 5.1754130194391066624e-13, 5.0291082725267428713e-12, 4.4721309725082660758e-11, 3.762775062517599803e-10, 3.0560232936142684245e-09, 2.4258201074369941878e-08, 1.8969295269383146249e-07, 1.4687810219326831175e-06, 1.1298692185571222767e-05, 8.6540548310009559815e-05, 0.00066094068550206401882, 0.0050381941644688631871, 0.038356111609708157251, 0.27103704347500323646, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0093429658193878890871, 1, ]).reshape((20, 7, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=TRUE
Boundary.knots=None
knots=np.array([10, 100, 1000, ])
output=np.array([1, 0.75141884282545001739, 0.47347381451739223301, 0.21613090194838843749, 0.049696178730542467372, 0.0013661273532290147976, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.24594066521407775827, 0.51146600459087232515, 0.73649217207800155016, 0.8374180350990879651, 0.77783093616193677011, 0.63185158416405007298, 0.45327074014730833751, 0.26317071169481792703, 0.10223303781294215686, 0.015729413486628138208, 5.1913250044765826493e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0026391300640233733565, 0.015039867450995366219, 0.047247102051768179176, 0.1123025082166889399, 0.21868817428784292911, 0.36168436874353226962, 0.52963938325215309533, 0.69654238826126158024, 0.81185888378284087885, 0.81901416673831617388, 0.71604356256126133751, 0.5618949499117826818, 0.38144450577487010179, 0.20035909960120826256, 0.06317094299551503922, 0.0053567803596460189172, 7.8294021104867289565e-10, 0, 0, 0, 1.3617399508643758347e-06, 2.0307441416267721758e-05, 0.00012974796415461092966, 0.00058265627417901637731, 0.0021107453507099383297, 0.0064420295146436082692, 0.01698600253976614155, 0.039859183304507203593, 0.084333549165298365979, 0.1600060050847842974, 0.26809274258076365438, 0.39566435336691235802, 0.51703688602850017553, 0.58200587206136744634, 0.52329606670761874554, 0.32126381870611603331, 0.10151738768556310688, 0.0078939921772932797328, 0, 0, 1.5649080494945798507e-10, 5.9986253804949622248e-09, 7.5940397022685528773e-08, 6.2142631098330448954e-07, 4.0139943226430085122e-06, 2.1989865743143280786e-05, 0.00010363219420155694861, 0.00042588199885955281988, 0.0015624557228769457226, 0.0051797373818038370402, 0.015437236122329209276, 0.040650688803865277221, 0.093964606272201794956, 0.18957613958735397564, 0.32176934741298385267, 0.41523761089925581569, 0.31715186300322989466, 0.072004316292680670131, 0, 0, 7.1504090682135632783e-15, 6.9828213556773098054e-13, 1.7290156975971226543e-11, 2.531906923450678459e-10, 2.8519588261786051741e-09, 2.7712025453436415246e-08, 2.4184791568371559048e-07, 1.8337667890099757592e-06, 1.2054075181315261422e-05, 7.0420380839424076883e-05, 0.00037180990877366712185, 0.0017643270358445378374, 0.0073398980933030329166, 0.026537861233464601213, 0.082516974679995302999, 0.20957251000715282352, 0.36763462897045218192, 0.25222621956325630421, 0, 0, 0, 0, 0, 0, 0, 5.430767431219823664e-15, 1.8655102027007631066e-11, 9.7376457669355941836e-10, 1.9440860511832082848e-08, 2.5692762827202482626e-07, 2.7355768273926624709e-06, 2.5679592827130744701e-05, 0.00021368415859150435886, 0.0015066158869563137275, 0.0090029382862195743431, 0.045606751586747915073, 0.18361012124256628764, 0.40566571262016642985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.2887678100169456754e-09, 4.196725335132502196e-07, 1.441162964935832764e-05, 0.00024372991766721753461, 0.0029625284410813028553, 0.030085998315248142776, 0.25286679352721525005, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0093429658193878890871, 1, ]).reshape((20, 9, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=12
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.13756522087537645382, 0.03408911379396852015, 0.0012620658543943507734, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.63650820114425754603, 0.56442389527296787932, 0.36731014666049194295, 0.15313548110822144954, 0.027890905208072903215, 0.00025587978478329849409, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.21582542819545830204, 0.37161327360106727324, 0.54958413949399687048, 0.64871936385648787393, 0.56949455728677522703, 0.3353237146654387546, 0.11624293381549648252, 0.013034128145880629485, 2.2307127685738320293e-06, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.010030255096273907323, 0.029534067226298671427, 0.080270381333806692381, 0.19129075916133608803, 0.37605173703394079165, 0.57813180094222393901, 0.65912921049582962052, 0.52910502202296083585, 0.27213902033160508598, 0.075690939564343556745, 0.0042624228385304534922, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7.0831944077479075004e-05, 0.00033917363922361448356, 0.0015696484900234396707, 0.006826920796842389158, 0.026357789549328776435, 0.084918297819850163677, 0.21706326994359459448, 0.42522472470932526356, 0.61970296224817356112, 0.65157393079529135616, 0.46867117785961504017, 0.21110309109651023696, 0.045544745506395555024, 0.00095384491778118137267, 0, 0, 0, 0, 0, 0, 6.2744556242273010119e-08, 4.764664739647606222e-07, 3.6181672866699010542e-06, 2.7475075557388156146e-05, 0.00020499609216608624608, 0.0013694324583970120431, 0.0075463637977772560911, 0.032410006481736260142, 0.10630697223991346367, 0.26202672571617779962, 0.48196986016032394851, 0.64730135891295237371, 0.6200897527822700761, 0.39505484728729772792, 0.15152949559297854143, 0.022327498493473717928, 5.4255821339140527268e-05, 0, 0, 0, 0, 0, 0, 1.5550084505986725427e-12, 1.4829716211306275165e-08, 8.7432930709587782537e-07, 1.8221879880749471527e-05, 0.00022607643762169348975, 0.0018471467411283852555, 0.010678853506751016453, 0.044774794636566098149, 0.13922623229659780719, 0.32186074315609991547, 0.55535177436803673245, 0.70594649934249964485, 0.65842055649705455433, 0.44019721934864292079, 0.20692042038665983683, 0.050925719374514837046, 0.0018364038370179928007, 0, 0, 0, 0, 0, 0, 6.7421415818675005971e-11, 4.2202475147942958715e-08, 1.6677264111419149931e-06, 2.9548372537568077557e-05, 0.0003214228907650466577, 0.0023601436034748866921, 0.012366085974335782813, 0.047309807190604125093, 0.13367877749039860924, 0.27692812785680914756, 0.41099272250632723491, 0.40841144311061738925, 0.22284961609469711163, 0.025059128845635693372, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.0448984495923750749e-09, 3.2161419941923913368e-07, 9.1740900392921949651e-06, 0.00013858698681402402734, 0.0013233320235020520645, 0.0087002635234140028586, 0.040418548886275447451, 0.13266384308046400009, 0.2919610966000151242, 0.36977070091947494834, 0.13378226109816304668, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.2555387649176344133e-13, 8.5594084815015888324e-08, 6.3942127780347038856e-06, 0.00014495964162350503772, 0.001897132263692037548, 0.015703476623777332805, 0.085263653984348167225, 0.27486427411742569982, 0.34006034727733425171, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.4090854418790210764e-09, 8.1360026955471610933e-06, 0.00038848261944930331932, 0.0074310477767932352905, 0.07805738431434718072, 0.38159617170452009294, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.2338141566056994185e-05, 0.0035323051795402063933, 0.11766568723732878654, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([])
output=np.array([0.9983344440741356296, 0.99750249875031249402, 0.99625562078283180778, 0.99438764201972595913, 0.99159092854883290613, 0.98740766028786153274, 0.98115924126864673127, 0.97184596123454425332, 0.95800877113085403103, 0.93754892830310510021, 0.90751599227918267054, 0.86391429083558635149, 0.801669064575886825, 0.71507663977921398502, 0.59936827269939108032, 0.45433823528182060159, 0.29067165046987419874, 0.13661155030676189193, 0.033621821670569602969, 0.0012124176758172098417, 0.0016644455553086624083, 0.002495003748750156472, 0.0037387626499230611904, 0.0055997301828166279672, 0.0083806908754831561242, 0.012528591196985617434, 0.018697687754151010031, 0.027833351892417868001, 0.041273795716988054272, 0.060850651795732749183, 0.088929278684076601413, 0.12824165844093327049, 0.18119239198615591513, 0.2480374111869024234, 0.32305865186741777872, 0.38825858275226748928, 0.40740939128236214328, 0.33403355026188807919, 0.1632256493056772062, 0.017159494075893101661, 1.1100003703292180217e-06, 2.4962518746875000594e-06, 5.6123532398144536609e-06, 1.2613583192428893271e-05, 2.8332643039885458129e-05, 6.3586947395334136954e-05, 0.00014252672253270057788, 0.00031885525421459126778, 0.00071127791904317637373, 0.0015797796625579053637, 0.0034857420363066235133, 0.0076146085944587566657, 0.016381164929213501424, 0.034414525059745272595, 0.069651262704549549154, 0.13271586265443335861, 0.22841224706537738287, 0.32670272008482859061, 0.31696810306481593145, 0.097144158424494309045, 3.7012349794238681896e-10, 1.2487503125000001559e-09, 4.2124242480468747114e-09, 1.4206263137512205132e-08, 4.7892153138227467571e-08, 1.6136291046154529314e-07, 5.4321868305356652586e-07, 1.8263821320052253243e-06, 6.1287830369107454558e-06, 2.0506795807293100125e-05, 6.8314944883560930534e-05, 0.00022606641535874693596, 0.00074049070575379688161, 0.0023874614910316710632, 0.0075083864312185822493, 0.022682692646808663012, 0.064029150684570476648, 0.15976638757864897178, 0.30776038811264622153, 0.27497860584540806395, 6.1707818930041155992e-14, 3.1234375000000000385e-13, 1.5808447265625000092e-12, 8.0000230407714837838e-12, 4.0477309670448300476e-11, 2.0474318975195293786e-10, 1.0351972331039468262e-09, 5.2306989582536832363e-09, 2.6404574434177521861e-08, 1.3309725534797475592e-07, 6.6943159388078163953e-07, 3.3557879908853077946e-06, 1.6736492419104248054e-05, 8.2813468459374991711e-05, 0.00040470096744437702128, 0.0019383686901439161829, 0.0089744140037595811904, 0.039065023078631577746, 0.14941007561236896439, 0.3891805482645412928, 4.1152263374485595055e-18, 3.1249999999999996265e-17, 2.3730468749999997491e-16, 1.802032470703124905e-15, 1.3684184074401855573e-14, 1.0391427281498912374e-13, 7.8909900918882334723e-13, 5.9922206010276265389e-12, 4.550342518905353542e-11, 3.4554163502937537769e-10, 2.6239567910043193503e-09, 1.9925671881689045749e-08, 1.5131057085157624606e-07, 1.1490146474041569126e-06, 8.7253299787253137094e-06, 6.625797452594535961e-05, 0.00050314649405639756945, 0.0038207686892407699206, 0.02901396223392209775, 0.22032477571384589954, ]).reshape((20, 6, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([100, ])
output=np.array([0.95099004990000002291, 0.92721650236562502823, 0.89244986942880866199, 0.84226263493375552738, 0.77123895238719930578, 0.67376233787836592448, 0.54625869959414907751, 0.39186905799172611076, 0.22752066204261220395, 0.088384183391962217735, 0.013598650651405662371, 4.4880894773220947292e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0.048976959490485305615, 0.072709651432435309926, 0.10738526002140330595, 0.15737069698548675212, 0.22795032016720712109, 0.32446067835465053353, 0.44989711207706667428, 0.59997610680291524332, 0.75567735422921566979, 0.87844628783911327119, 0.92474207754597603781, 0.89365801028359981295, 0.82931282542333117913, 0.73973445494401424138, 0.62003614417178387619, 0.47000507098119359561, 0.30069481083090426887, 0.14132229342078814205, 0.034781194831623722663, 0.0012542251818798723622, 3.2979557543950620822e-05, 7.3809001036874996929e-05, 0.00016474549918236330078, 0.00036624856893145208737, 0.00080932434818232947688, 0.0017723123294799305975, 0.003828742087843573557, 0.008104291378262778317, 0.016639190251049165714, 0.032657698828719619599, 0.060108147688838667322, 0.10184799113601375464, 0.15884341152280503917, 0.23108234071251923525, 0.31281804868450863166, 0.38543973833058026157, 0.4110892044013776947, 0.34067876601502938838, 0.16765476842547211156, 0.017707951623962527032, 1.1050122962962964101e-08, 3.7191558750000007188e-08, 1.250033797265625178e-07, 4.1927333935913091575e-07, 1.401894586697731242e-06, 4.665382495865281394e-06, 1.5415847866808392633e-05, 5.0391939592239989756e-05, 0.00016203956276710770662, 0.00050812727751784608765, 0.0015332452896675867124, 0.004365181610267205789, 0.011468673667365519159, 0.027632876244132381638, 0.061266201119033726619, 0.12400124625180757032, 0.22211304163999795458, 0.32622078746654581405, 0.32211683874203467237, 0.099883337969340235674, 1.8476543209876543964e-12, 9.3431250000000011463e-12, 4.7218886718750000078e-11, 2.3843292297363282786e-10, 1.2024140499687195818e-09, 6.0518902752095459008e-09, 3.0369400855124124217e-08, 1.5170773682471650721e-07, 7.5254925311084902029e-07, 3.6922964379636862066e-06, 1.7800105408249686863e-05, 8.3338305189489776193e-05, 0.00037055336224994447902, 0.0015169299478213021069, 0.0056546686833628886926, 0.019188949419050076173, 0.058577982030935032975, 0.15402658068596292162, 0.30726533809094308536, 0.28101637370320353693, 1.2345679012345678054e-16, 9.3749999999999994959e-16, 7.1191406249999982613e-15, 5.4060974121093752673e-14, 4.1052552223205572084e-13, 3.1174281844496722735e-12, 2.3672970275664694964e-11, 1.7976661803082883009e-10, 1.3651027556716062823e-09, 1.0366249050881264123e-08, 7.8718703730129575545e-08, 5.9777015645067147174e-07, 4.5359107007994105463e-06, 3.3361176067584402466e-05, 0.00022366759792994563949, 0.0013435210788023243395, 0.0072639461407535297829, 0.035100831436999477275, 0.14396679069931464512, 0.39291034738734598175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.1354749159583518215e-10, 3.6975445151343504115e-08, 1.2697433810604620809e-06, 2.1473938565867883887e-05, 0.00026101495603127997146, 0.0026507409746741200131, 0.024215069210611606804, 0.20722776413426768904, ]).reshape((20, 7, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([1000, ])
output=np.array([0.9950099900049993451, 0.99252246627530493761, 0.98880051122183698631, 0.98323852246469989336, 0.9749424948799998436, 0.96260353805275378214, 0.94432964343891578896, 0.91744015090894337483, 0.87825768212583654737, 0.82200480749416604542, 0.74306451043607213425, 0.63613393199503898146, 0.4991479566148370739, 0.33884847274276985729, 0.17798493325279582389, 0.056116623078022197235, 0.0047585869404442801556, 6.9550902096181903072e-10, 0, 0, 0.0049866811037051986949, 0.0074700487125113432946, 0.011182664341492391802, 0.016723679332538900905, 0.024972650503249212151, 0.037206183352661716113, 0.055244396744596628579, 0.081608715488401706306, 0.11962663350752600344, 0.17331618121340841565, 0.2466772227646655824, 0.34167053826082144363, 0.45378166194157465441, 0.56434225055466613608, 0.6320750091698927875, 0.59733241830569760999, 0.42886959529414492298, 0.2049173244168793484, 0.050432732505854400984, 0.0018186265137258147626, 3.3277811103950621097e-06, 7.4812668695624991679e-06, 1.6811804138396487754e-05, 3.7755607955489316522e-05, 8.4711061600122608767e-05, 0.00018979511914756760698, 0.00042433325892820140772, 0.00094567009442594711366, 0.002097376821719070146, 0.0046178870868948916628, 0.010055306643782090104, 0.021527218530989256778, 0.04489775700844654549, 0.089884991503020608694, 0.16855047321618027434, 0.28372166497555245668, 0.39667928927647067017, 0.39859166318439254173, 0.21962210770558857065, 0.024829927856976751616, 1.1100002962962965174e-09, 3.7443772500000006212e-09, 1.2627790523437500951e-08, 4.2570810898681638324e-08, 1.4343375976686856963e-07, 4.8286151921739802212e-07, 1.6234543349501860428e-06, 5.4478341089133651746e-06, 1.8228467705229433379e-05, 6.0725950389411787269e-05, 0.000200959732568890164, 0.00065830362619350942834, 0.0021228688895969845957, 0.0066792918381076149537, 0.020201657448734186562, 0.057212961493873837338, 0.14427872595983071147, 0.29075824853504678158, 0.36564110074442957021, 0.13330127370825312072, 1.8509876543209877828e-13, 9.3684375000000001865e-13, 4.7411103515625013439e-12, 2.3989256927490236273e-11, 1.2134982390689849712e-10, 6.1360608361896876588e-10, 3.1008571052567070342e-09, 1.5656143551154886378e-08, 7.8940702751398238619e-08, 3.9721851623374797589e-07, 1.9925510408963188817e-06, 9.9478099413657914188e-06, 4.930161383220328208e-05, 0.00024154631749370003959, 0.0011617509224607792256, 0.0054175582232760775836, 0.023904363046940359239, 0.094270457100450164023, 0.27882003179675446392, 0.34581727191398570209, 1.2345679012345680828e-17, 9.3750000000000019611e-17, 7.1191406250000002335e-16, 5.4060974121093743207e-15, 4.1052552223205574609e-14, 3.1174281844496730813e-13, 2.3672970275664701427e-12, 1.7976661803082880424e-11, 1.3651027556716061272e-10, 1.0366249050881260814e-09, 7.8718703730129572237e-09, 5.9777015645067147174e-08, 4.5393171255472860584e-07, 3.4470439422124707377e-06, 2.6175989936175949598e-05, 0.00019877392357783613304, 0.0015094394821691928168, 0.011462306067722308894, 0.084705097520176186876, 0.41086218643981919918, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00077892972719669731786, 0.083370713567239546071, ]).reshape((20, 7, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=TRUE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([10, 100, 1000, ])
output=np.array([0.59048999999999984833, 0.44370531250000000423, 0.27958155273437496069, 0.12762313629150390248, 0.029345096578598028197, 0.00080668454080820085113, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.40055561099999992258, 0.53723465540624992798, 0.68096479632714856933, 0.79404388738027953387, 0.82432650645400162848, 0.74772850370839760714, 0.60695411066016569102, 0.43541006443525137604, 0.25280073560290239332, 0.098204648213291390046, 0.015109611834895182725, 4.9867660859134372328e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0.0089449327889999999397, 0.01902900664771874778, 0.039353322248692389207, 0.078015642723590794549, 0.14535820079903546964, 0.24864829948443537, 0.38546024005089984943, 0.54588573189455047441, 0.70480915137930777448, 0.81344804238272472308, 0.81717844525199323513, 0.71389962178539645432, 0.56021095018500233209, 0.38030131620961826755, 0.19975862317934431345, 0.062981619616186521049, 0.0053407260835513798575, 7.8059373845321992395e-10, 0, 0, 9.4545814444444459148e-06, 3.1017290343749996553e-05, 0.00010028811495410157556, 0.00031713361145809936415, 0.00096922483761113851879, 0.0028119072707210087531, 0.0075647083349296134064, 0.01861553313384527869, 0.042046394339120052308, 0.087124325583113268467, 0.16371060089498334911, 0.27411331174625735985, 0.40781324509205862938, 0.54286075051770132927, 0.63352683070691229172, 0.61289306201898841042, 0.44453962951650222157, 0.21269241367644950436, 0.052346279846925344859, 0.0018876298723944570646, 1.6294444444444447802e-09, 8.1548437499999982349e-09, 4.0568422851562507446e-08, 1.9994451278686527012e-07, 9.7096128099918361425e-07, 4.602189952521174856e-06, 2.0919648984453888767e-05, 8.8510989072612499115e-05, 0.0003426071500521665281, 0.0012159913347531664096, 0.0039613824662355322237, 0.011728070771626651625, 0.030981021316488138728, 0.072628957837475555115, 0.15091202659488145432, 0.27128094978421118944, 0.39491276471324787689, 0.40566968711467132902, 0.22597501897826155481, 0.025700635234072819607, 1.1111111111111111028e-13, 8.4374999999999991361e-13, 6.4072265625000016033e-12, 4.8654876708984379097e-11, 3.694729700088501736e-10, 2.8056853660047051398e-09, 2.1305018189503776537e-08, 1.5953977978988917004e-07, 1.1111370996233737328e-06, 6.9846696024166045466e-06, 3.9856249948076781855e-05, 0.00020802815256623063945, 0.00098445759075916142167, 0.0041227618062229087065, 0.015186511992247679614, 0.04905127538388008579, 0.1347633037922978394, 0.28642046903795781443, 0.37096041450757122337, 0.1373866644432741313, 0, 0, 0, 0, 0, 0, 2.1835286481805527197e-15, 7.5005881261891668233e-12, 3.9151793493690686266e-10, 7.8165151445884371071e-09, 1.0330194469679949402e-07, 1.0998832941312460716e-06, 1.0325449816590390154e-05, 8.6094485881001237893e-05, 0.00061191613127527461416, 0.0037238993946882535668, 0.01960252770274439893, 0.08667615291637777164, 0.27442290749348607903, 0.35255941616703945218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.6587525069769108936e-10, 1.1914310104321792976e-07, 4.09139533897260065e-06, 6.9193802045574309841e-05, 0.00084104819165634655664, 0.0085412764739499439509, 0.075516449446559150149, 0.39909494071597967357, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00077892972719669731786, 0.083370713567239546071, ]).reshape((20, 9, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=12
intercept=FALSE
Boundary.knots=None
knots=None
output=np.array([0, 0.66169375447141842717, 0.42704385876993139481, 0.10949465799552411671, 0.0045536628406788173459, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.16320829536765205092, 0.50699791406824767925, 0.63821768315482607647, 0.43263056231744068114, 0.15013545271065373288, 0.016177354520514077713, 1.5876171140511428811e-06, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0058674236731072158813, 0.063738396787633516682, 0.24043272488013067711, 0.50054000504763473955, 0.64199088129854386953, 0.51113227122624471654, 0.23070045750246975791, 0.045936684143309251815, 0.00066416458897031876996, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.5532476724270158346e-05, 0.0011936972661650952464, 0.011779052740953399256, 0.061287867695344819263, 0.20002288232936923928, 0.43156920172035251326, 0.62168249332663905182, 0.58254996098081013312, 0.32664310035477628347, 0.092349295039142950681, 0.0054390297948912662632, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.1496907292528123154e-08, 3.0758698527859495562e-06, 7.5879789673663857776e-05, 0.00098724833216487398006, 0.0078266015648352053879, 0.040722255850196634186, 0.14383543945950985621, 0.34844598770889789741, 0.57762867947116447453, 0.6345732251821071257, 0.43704729558185051452, 0.16650826117204259313, 0.023068804299786735412, 3.4807107343935620814e-05, 0, 0, 0, 0, 0, 0, 0, 0, 1.4388921206346279741e-09, 6.5376673591293467664e-07, 2.4182089760364024493e-05, 0.00039880176232783617072, 0.0037728522997899590664, 0.022917112728778440273, 0.09334968530661211239, 0.26084062164632215719, 0.49935247038577756928, 0.64262810933455249973, 0.53564397016856835076, 0.2604583835482605636, 0.059387695355091404958, 0.0015752311746957685552, 0, 0, 0, 0, 0, 0, 0, 0, 6.8376488468020759222e-12, 1.1492036416820246223e-07, 7.1697944771843335622e-06, 0.00015024167017123804383, 0.0017124892221411901899, 0.012184251164569406822, 0.05741986723560143363, 0.1846190190189749003, 0.40690335279758527154, 0.61033896958284106216, 0.60106063282500987732, 0.35935886624758728303, 0.11099693368003141214, 0.0086311215629592420023, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.2768033036904166236e-08, 1.8810563357318595251e-06, 5.2605966584901652059e-05, 0.00074031169779739149084, 0.0062001835283552559838, 0.03359211253079281978, 0.12126275319647328299, 0.28972724543117178708, 0.43422014732068858756, 0.34126033432252839139, 0.077899048558433395262, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.0012735173756063062e-09, 1.0253040817926208576e-06, 4.44269427274169616e-05, 0.0007905173401505205099, 0.0078092535818893079463, 0.047648780279836128182, 0.17901740350372433164, 0.37155904838956399505, 0.26547319923364387506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.3473917264272251468e-12, 1.2428631162803442157e-06, 9.5832983192035734268e-05, 0.0021733294132454694138, 0.025265394711489429919, 0.1581848201187764924, 0.40157681487049512459, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2.3166956453278957354e-06, 0.00056295704181467877848, 0.017992654275869164604, 0.22701602442886983924, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.2092132305915514147e-06, 0.019403791345598601914, 1, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([])
output=np.array([0, 0.0011272234177996938269, 0.0028142442804627715303, 0.0053362072774053633667, 0.0090999196124958610377, 0.014702372431044495246, 0.023009568696928141274, 0.035255048800207078319, 0.053144756365466998271, 0.078922539871719557536, 0.11527760116980134697, 0.16482753559964863355, 0.22865514515079901625, 0.30306893078627178406, 0.37385911082806172478, 0.40954833229285447782, 0.36355794566479110452, 0.2119513600790149388, 0.041217298467029310494, 0, 0, 5.0882687398560041373e-07, 3.1769391294621280289e-06, 1.1451280924188917779e-05, 3.3428733100138665235e-05, 8.7761935886847988286e-05, 0.00021681208262686383053, 0.00051561316170019592855, 0.0011946804469067555735, 0.0027131220507094727483, 0.0060506596144855023106, 0.013230141811374926397, 0.028210083582453096551, 0.058035333309565624582, 0.11300958063559926603, 0.20116335451605230067, 0.30568795867586873172, 0.33877798094471850421, 0.16475763686175207146, 0, 0, 1.1484182443411814437e-10, 1.7931887260774516498e-09, 1.2286988490864389756e-08, 6.1400553206305291637e-08, 2.6193586874264870438e-07, 1.0214767558696939837e-06, 3.7704802796487805225e-06, 1.3428054504627923681e-05, 4.6634530984498030184e-05, 0.00015879269432593164958, 0.00053096908751411363639, 0.00174019442073816092, 0.0055566565395111476577, 0.017080184681265514479, 0.049404053208565550104, 0.12851476524402274948, 0.27074730808567093465, 0.32929231067852438031, 0, 0, 1.2959854631941128047e-14, 5.06072933143619642e-13, 6.5918427455453850986e-12, 5.6389033989814727996e-11, 3.9088927700060016774e-10, 2.406265255469648607e-09, 1.378603437152614992e-08, 7.5464802427343835863e-08, 4.0078909822273000137e-07, 2.0836670196190539855e-06, 1.0654767572225721294e-05, 5.3673655611779830157e-05, 0.00026601408260545672708, 0.0012907432586925205419, 0.0060666130749972404027, 0.027014549341864656923, 0.10818900424286860551, 0.32906949849914446382, 0, 0, 5.8500579526198340832e-19, 5.7129472193553077444e-17, 1.4145822896898457119e-15, 2.0714622186680568112e-14, 2.3333104795078233906e-13, 2.2673496764008742317e-12, 2.0162390952550372537e-11, 1.6964293385718098169e-10, 1.3777936465799164788e-09, 1.0936695210263550545e-08, 8.5522170452256458058e-08, 6.6219297623306537514e-07, 5.0939619277349284707e-06, 3.9016396858695978102e-05, 0.00029798198173219142288, 0.00227144600780565184, 0.017292671490362138825, 0.13153873483331313121, 1, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([100, ])
output=np.array([0, 0.024987397851780435848, 0.061486796626462968118, 0.1140799960900419352, 0.1882939537142028219, 0.28966625638367565765, 0.42117968175620157378, 0.57798465087862005429, 0.73943782746930653005, 0.86397935690336380432, 0.90462871246453147034, 0.85976637084822715718, 0.77601114642224255924, 0.66267521310641597232, 0.51785842958629058064, 0.34911766989097126057, 0.18104199448456753663, 0.055522321426452851678, 0.0043174157141209739547, 0, 0, 1.1333836374491041766e-05, 7.0253868335539153452e-05, 0.00025049169236306427153, 0.00071939598049462782393, 0.0018428967293926601032, 0.0043879998552115674279, 0.0098727402942039785283, 0.021048289930652556295, 0.042207103219050953746, 0.078361329513138483494, 0.1323267251748408424, 0.20305646979998087653, 0.28625091024051202426, 0.36712456913477936604, 0.41237454577086030127, 0.37209382828918063923, 0.21926721332006066101, 0.042943027456719351509, 0, 0, 2.5642163564224008165e-09, 3.9893896948733327568e-08, 2.7186958324120860093e-07, 1.3475047468249140318e-06, 5.6780166980285093331e-06, 2.1734507210374597486e-05, 7.8000238621073664019e-05, 0.00026616938183067457386, 0.00086607259863206911138, 0.0026688434677071591963, 0.0076602480569788727188, 0.020032890189762748295, 0.047362176442216685768, 0.10112516340575794516, 0.19128545657973258787, 0.30258229747242648688, 0.34436724521558481626, 0.17045464690987927048, 0, 0, 2.8979008849349871451e-13, 1.1299684644739179399e-11, 1.4686295227238142049e-10, 1.2521970298985838129e-09, 8.6372943219355255736e-09, 5.2772400925513216102e-08, 2.9891245041530529781e-07, 1.607880484564848822e-06, 8.3111564072833673416e-06, 4.1402961295506068027e-05, 0.00019754788193526286806, 0.00088468396831814228969, 0.0036015013400145573161, 0.013149579882492583999, 0.042768561910798960635, 0.12037399719492403172, 0.26730426029673903798, 0.33672081019045274619, 0, 0, 1.309371682920953332e-17, 1.2786832841024935257e-15, 3.1661464010829005262e-14, 4.6363882090260461786e-13, 5.2224622287067364928e-12, 5.0748274386406234902e-11, 4.5127867086219768747e-10, 3.7969821085404869e-09, 3.0838053235562167103e-08, 2.44787301750460405e-07, 1.9141743408195724709e-06, 1.4809071760067401229e-05, 0.00011002044182894339316, 0.0007361307065949982114, 0.0043501410333869746858, 0.022648326644076191561, 0.10074752233050462968, 0.32871166293410319925, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.4793570769921117731e-10, 1.7842901170031562027e-07, 6.1272840845677704696e-06, 0.00010362481425005589904, 0.0012595559148250148798, 0.012791437410658173385, 0.11685243679472454015, 1, ]).reshape((20, 6, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([1000, ])
output=np.array([0, 0.0024988701975066367374, 0.0062335737743966241481, 0.011805108206123089004, 0.020094017057672219906, 0.032374436158623724757, 0.050454096263104174225, 0.076816980711751164934, 0.11469423978741608017, 0.16787638509201657788, 0.23985925947403505254, 0.33155764168655321722, 0.4364690436594241274, 0.53267348082579712987, 0.57514542430970139186, 0.50463302930351572329, 0.30623588293439429897, 0.096587849561305616497, 0.0075106712808647091081, 0, 0, 1.1282962103271197581e-06, 7.0417854820895302466e-06, 2.536640667453833115e-05, 7.3981096505374880712e-05, 0.00019395448667024030114, 0.00047814948330325205252, 0.0011335173229715391711, 0.0026138368581788955919, 0.0058931764589364287604, 0.012998501527337540801, 0.027945386445582046098, 0.058044007669012950834, 0.1145680954987259581, 0.2086069927114643785, 0.33148565972609811414, 0.41061823564260202524, 0.3066625410662744966, 0.068889778730265194273, 0, 0, 2.5469057156714312774e-10, 3.976025713192789869e-09, 2.7235422387831816846e-08, 1.3603757883811768205e-07, 5.7993308882550318022e-07, 2.2591989366454093128e-06, 8.3259563410340815473e-06, 2.9580849000601624262e-05, 0.00010235971125482330811, 0.00034661758311907198181, 0.001149214388416119545, 0.0037170166582407206807, 0.011623043605661884797, 0.034525979049628140183, 0.094171296281739258482, 0.21954225794050497012, 0.36514412761590397949, 0.24346326935859208263, 0, 0, 2.8744101198050118059e-14, 1.1223436953991581798e-12, 1.4617253735361878953e-11, 1.2501806315302350017e-10, 8.6638386346496238246e-10, 5.3311050540562872207e-09, 3.0523721489433262118e-08, 1.6692682142232341016e-07, 8.8525020719146049419e-07, 4.5921420319874677743e-06, 2.3401813204582281116e-05, 0.0001172621098580812533, 0.00057627166059595505247, 0.0027575294458836020588, 0.012651024092369157759, 0.053782977897194939043, 0.19324936944781076487, 0.3997562713358867037, 0, 0, 1.2975755416333770318e-18, 1.2671636148763449243e-16, 3.1376225596317031531e-15, 4.5946189458816680778e-14, 5.1754130194391066624e-13, 5.0291082725267428713e-12, 4.4721309725082660758e-11, 3.762775062517599803e-10, 3.0560232936142684245e-09, 2.4258201074369941878e-08, 1.8969295269383146249e-07, 1.4687810219326831175e-06, 1.1298692185571222767e-05, 8.6540548310009559815e-05, 0.00066094068550206401882, 0.0050381941644688631871, 0.038356111609708157251, 0.27103704347500323646, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0093429658193878890871, 1, ]).reshape((20, 6, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=FALSE
Boundary.knots=None
knots=np.array([10, 100, 1000, ])
output=np.array([0, 0.24594066521407775827, 0.51146600459087232515, 0.73649217207800155016, 0.8374180350990879651, 0.77783093616193677011, 0.63185158416405007298, 0.45327074014730833751, 0.26317071169481792703, 0.10223303781294215686, 0.015729413486628138208, 5.1913250044765826493e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0026391300640233733565, 0.015039867450995366219, 0.047247102051768179176, 0.1123025082166889399, 0.21868817428784292911, 0.36168436874353226962, 0.52963938325215309533, 0.69654238826126158024, 0.81185888378284087885, 0.81901416673831617388, 0.71604356256126133751, 0.5618949499117826818, 0.38144450577487010179, 0.20035909960120826256, 0.06317094299551503922, 0.0053567803596460189172, 7.8294021104867289565e-10, 0, 0, 0, 1.3617399508643758347e-06, 2.0307441416267721758e-05, 0.00012974796415461092966, 0.00058265627417901637731, 0.0021107453507099383297, 0.0064420295146436082692, 0.01698600253976614155, 0.039859183304507203593, 0.084333549165298365979, 0.1600060050847842974, 0.26809274258076365438, 0.39566435336691235802, 0.51703688602850017553, 0.58200587206136744634, 0.52329606670761874554, 0.32126381870611603331, 0.10151738768556310688, 0.0078939921772932797328, 0, 0, 1.5649080494945798507e-10, 5.9986253804949622248e-09, 7.5940397022685528773e-08, 6.2142631098330448954e-07, 4.0139943226430085122e-06, 2.1989865743143280786e-05, 0.00010363219420155694861, 0.00042588199885955281988, 0.0015624557228769457226, 0.0051797373818038370402, 0.015437236122329209276, 0.040650688803865277221, 0.093964606272201794956, 0.18957613958735397564, 0.32176934741298385267, 0.41523761089925581569, 0.31715186300322989466, 0.072004316292680670131, 0, 0, 7.1504090682135632783e-15, 6.9828213556773098054e-13, 1.7290156975971226543e-11, 2.531906923450678459e-10, 2.8519588261786051741e-09, 2.7712025453436415246e-08, 2.4184791568371559048e-07, 1.8337667890099757592e-06, 1.2054075181315261422e-05, 7.0420380839424076883e-05, 0.00037180990877366712185, 0.0017643270358445378374, 0.0073398980933030329166, 0.026537861233464601213, 0.082516974679995302999, 0.20957251000715282352, 0.36763462897045218192, 0.25222621956325630421, 0, 0, 0, 0, 0, 0, 0, 5.430767431219823664e-15, 1.8655102027007631066e-11, 9.7376457669355941836e-10, 1.9440860511832082848e-08, 2.5692762827202482626e-07, 2.7355768273926624709e-06, 2.5679592827130744701e-05, 0.00021368415859150435886, 0.0015066158869563137275, 0.0090029382862195743431, 0.045606751586747915073, 0.18361012124256628764, 0.40566571262016642985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.2887678100169456754e-09, 4.196725335132502196e-07, 1.441162964935832764e-05, 0.00024372991766721753461, 0.0029625284410813028553, 0.030085998315248142776, 0.25286679352721525005, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.0093429658193878890871, 1, ]).reshape((20, 8, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=12
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=None
output=np.array([0.59169275909036811445, 0.45594291761992611356, 0.23025182795481774489, 0.058881690084655928519, 0.00244877119161214735, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2905201731274964505, 0.4652923293619554701, 0.60774274744642686752, 0.58452788522997400911, 0.36691850516129698168, 0.12666647389296178949, 0.013648531489621140711, 1.3394428704094372146e-06, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.021589057498422929704, 0.061252310080865124409, 0.15611979121414976124, 0.33262545668279458466, 0.54628842278228206819, 0.6385151816856337037, 0.49255505710881447579, 0.221198927694986891, 0.044044707273317475205, 0.00063680989274803034839, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00027107258187882916, 0.00127840807191650788, 0.0057589114783163615832, 0.023748086219301876854, 0.082775728340449605813, 0.22525005798130448564, 0.44911867244320990977, 0.62614581126197998984, 0.57974912164267822234, 0.32404333353141845375, 0.091606580575315788018, 0.0053952866770240375319, 0, 0, 0, 0, 0, 0, 0, 0, 4.9568448953450556364e-07, 3.7641040924026516272e-06, 2.8583665451682636297e-05, 0.00021688034438166792803, 0.0015679187576233750197, 0.0095441043435020560953, 0.04427882227566239115, 0.1488738995058955239, 0.35313880391702157091, 0.58025580099074469675, 0.63531593964593435775, 0.4370910386997177155, 0.16650826117204259313, 0.023068804299786735412, 3.4807107343935620814e-05, 0, 0, 0, 0, 0, 0, 0, 0, 1.4388921206346279741e-09, 6.5376673591293467664e-07, 2.4182089760364024493e-05, 0.00039880176232783617072, 0.0037728522997899590664, 0.022917112728778440273, 0.09334968530661211239, 0.26084062164632215719, 0.49935247038577756928, 0.64262810933455249973, 0.53564397016856835076, 0.2604583835482605636, 0.059387695355091404958, 0.0015752311746957685552, 0, 0, 0, 0, 0, 0, 0, 0, 6.8376488468020759222e-12, 1.1492036416820246223e-07, 7.1697944771843335622e-06, 0.00015024502361066615209, 0.0017129832691462772674, 0.012198067843520864206, 0.057614376649993519208, 0.18625055189529041155, 0.41578120601587292837, 0.64273379913532080465, 0.68051701302793721204, 0.48636048533160558538, 0.22961899064651025704, 0.056512123157649481187, 0.0020378520142550187594, 0, 0, 0, 0, 0, 0, 0, 0, 9.4145936087959968535e-09, 1.3870093306447404973e-06, 3.87897484409095467e-05, 0.00054627415024945797078, 0.0045890968808638254312, 0.025078293403640077724, 0.092478958938429417502, 0.23258667138901650828, 0.39413899790146145197, 0.42298881551441958049, 0.23945155313138169473, 0.027468762021704565962, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.4046605238618360691e-10, 5.5343723764345201347e-07, 2.3980715961026268597e-05, 0.00042724725529509507927, 0.004257128234965919765, 0.026669687498425763417, 0.10780673176995581031, 0.27416234388481963702, 0.37835275864003403701, 0.14369344182750692918, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.2897006858339270209e-12, 4.7885683674533647596e-07, 3.6923035679543052192e-05, 0.00083835438251815758755, 0.0099780154986416052382, 0.068735165020702704286, 0.25981214905110344704, 0.35274625004443893594, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5.7834701086365511565e-07, 0.00014053832363986818919, 0.0044940505180239382829, 0.063888867507214189279, 0.37188043953033556033, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6.344155240374716805e-07, 0.0019825485126170991519, 0.10217325456175885279, ]).reshape((20, 12, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([])
output=np.array([0.0016644455553086624083, 0.002495003748750156472, 0.0037387626499230611904, 0.0055997301828166279672, 0.0083806908754831561242, 0.012528591196985617434, 0.018697687754151010031, 0.027833351892417868001, 0.041273795716988054272, 0.060850651795732749183, 0.088929278684076601413, 0.12824165844093327049, 0.18119239198615591513, 0.2480374111869024234, 0.32305865186741777872, 0.38825858275226748928, 0.40740939128236214328, 0.33403355026188807919, 0.1632256493056772062, 0.017159494075893101661, 1.1100003703292180217e-06, 2.4962518746875000594e-06, 5.6123532398144536609e-06, 1.2613583192428893271e-05, 2.8332643039885458129e-05, 6.3586947395334136954e-05, 0.00014252672253270057788, 0.00031885525421459126778, 0.00071127791904317637373, 0.0015797796625579053637, 0.0034857420363066235133, 0.0076146085944587566657, 0.016381164929213501424, 0.034414525059745272595, 0.069651262704549549154, 0.13271586265443335861, 0.22841224706537738287, 0.32670272008482859061, 0.31696810306481593145, 0.097144158424494309045, 3.7012349794238681896e-10, 1.2487503125000001559e-09, 4.2124242480468747114e-09, 1.4206263137512205132e-08, 4.7892153138227467571e-08, 1.6136291046154529314e-07, 5.4321868305356652586e-07, 1.8263821320052253243e-06, 6.1287830369107454558e-06, 2.0506795807293100125e-05, 6.8314944883560930534e-05, 0.00022606641535874693596, 0.00074049070575379688161, 0.0023874614910316710632, 0.0075083864312185822493, 0.022682692646808663012, 0.064029150684570476648, 0.15976638757864897178, 0.30776038811264622153, 0.27497860584540806395, 6.1707818930041155992e-14, 3.1234375000000000385e-13, 1.5808447265625000092e-12, 8.0000230407714837838e-12, 4.0477309670448300476e-11, 2.0474318975195293786e-10, 1.0351972331039468262e-09, 5.2306989582536832363e-09, 2.6404574434177521861e-08, 1.3309725534797475592e-07, 6.6943159388078163953e-07, 3.3557879908853077946e-06, 1.6736492419104248054e-05, 8.2813468459374991711e-05, 0.00040470096744437702128, 0.0019383686901439161829, 0.0089744140037595811904, 0.039065023078631577746, 0.14941007561236896439, 0.3891805482645412928, 4.1152263374485595055e-18, 3.1249999999999996265e-17, 2.3730468749999997491e-16, 1.802032470703124905e-15, 1.3684184074401855573e-14, 1.0391427281498912374e-13, 7.8909900918882334723e-13, 5.9922206010276265389e-12, 4.550342518905353542e-11, 3.4554163502937537769e-10, 2.6239567910043193503e-09, 1.9925671881689045749e-08, 1.5131057085157624606e-07, 1.1490146474041569126e-06, 8.7253299787253137094e-06, 6.625797452594535961e-05, 0.00050314649405639756945, 0.0038207686892407699206, 0.02901396223392209775, 0.22032477571384589954, ]).reshape((20, 5, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([100, ])
output=np.array([0.048976959490485305615, 0.072709651432435309926, 0.10738526002140330595, 0.15737069698548675212, 0.22795032016720712109, 0.32446067835465053353, 0.44989711207706667428, 0.59997610680291524332, 0.75567735422921566979, 0.87844628783911327119, 0.92474207754597603781, 0.89365801028359981295, 0.82931282542333117913, 0.73973445494401424138, 0.62003614417178387619, 0.47000507098119359561, 0.30069481083090426887, 0.14132229342078814205, 0.034781194831623722663, 0.0012542251818798723622, 3.2979557543950620822e-05, 7.3809001036874996929e-05, 0.00016474549918236330078, 0.00036624856893145208737, 0.00080932434818232947688, 0.0017723123294799305975, 0.003828742087843573557, 0.008104291378262778317, 0.016639190251049165714, 0.032657698828719619599, 0.060108147688838667322, 0.10184799113601375464, 0.15884341152280503917, 0.23108234071251923525, 0.31281804868450863166, 0.38543973833058026157, 0.4110892044013776947, 0.34067876601502938838, 0.16765476842547211156, 0.017707951623962527032, 1.1050122962962964101e-08, 3.7191558750000007188e-08, 1.250033797265625178e-07, 4.1927333935913091575e-07, 1.401894586697731242e-06, 4.665382495865281394e-06, 1.5415847866808392633e-05, 5.0391939592239989756e-05, 0.00016203956276710770662, 0.00050812727751784608765, 0.0015332452896675867124, 0.004365181610267205789, 0.011468673667365519159, 0.027632876244132381638, 0.061266201119033726619, 0.12400124625180757032, 0.22211304163999795458, 0.32622078746654581405, 0.32211683874203467237, 0.099883337969340235674, 1.8476543209876543964e-12, 9.3431250000000011463e-12, 4.7218886718750000078e-11, 2.3843292297363282786e-10, 1.2024140499687195818e-09, 6.0518902752095459008e-09, 3.0369400855124124217e-08, 1.5170773682471650721e-07, 7.5254925311084902029e-07, 3.6922964379636862066e-06, 1.7800105408249686863e-05, 8.3338305189489776193e-05, 0.00037055336224994447902, 0.0015169299478213021069, 0.0056546686833628886926, 0.019188949419050076173, 0.058577982030935032975, 0.15402658068596292162, 0.30726533809094308536, 0.28101637370320353693, 1.2345679012345678054e-16, 9.3749999999999994959e-16, 7.1191406249999982613e-15, 5.4060974121093752673e-14, 4.1052552223205572084e-13, 3.1174281844496722735e-12, 2.3672970275664694964e-11, 1.7976661803082883009e-10, 1.3651027556716062823e-09, 1.0366249050881264123e-08, 7.8718703730129575545e-08, 5.9777015645067147174e-07, 4.5359107007994105463e-06, 3.3361176067584402466e-05, 0.00022366759792994563949, 0.0013435210788023243395, 0.0072639461407535297829, 0.035100831436999477275, 0.14396679069931464512, 0.39291034738734598175, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.1354749159583518215e-10, 3.6975445151343504115e-08, 1.2697433810604620809e-06, 2.1473938565867883887e-05, 0.00026101495603127997146, 0.0026507409746741200131, 0.024215069210611606804, 0.20722776413426768904, ]).reshape((20, 6, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([1000, ])
output=np.array([0.0049866811037051986949, 0.0074700487125113432946, 0.011182664341492391802, 0.016723679332538900905, 0.024972650503249212151, 0.037206183352661716113, 0.055244396744596628579, 0.081608715488401706306, 0.11962663350752600344, 0.17331618121340841565, 0.2466772227646655824, 0.34167053826082144363, 0.45378166194157465441, 0.56434225055466613608, 0.6320750091698927875, 0.59733241830569760999, 0.42886959529414492298, 0.2049173244168793484, 0.050432732505854400984, 0.0018186265137258147626, 3.3277811103950621097e-06, 7.4812668695624991679e-06, 1.6811804138396487754e-05, 3.7755607955489316522e-05, 8.4711061600122608767e-05, 0.00018979511914756760698, 0.00042433325892820140772, 0.00094567009442594711366, 0.002097376821719070146, 0.0046178870868948916628, 0.010055306643782090104, 0.021527218530989256778, 0.04489775700844654549, 0.089884991503020608694, 0.16855047321618027434, 0.28372166497555245668, 0.39667928927647067017, 0.39859166318439254173, 0.21962210770558857065, 0.024829927856976751616, 1.1100002962962965174e-09, 3.7443772500000006212e-09, 1.2627790523437500951e-08, 4.2570810898681638324e-08, 1.4343375976686856963e-07, 4.8286151921739802212e-07, 1.6234543349501860428e-06, 5.4478341089133651746e-06, 1.8228467705229433379e-05, 6.0725950389411787269e-05, 0.000200959732568890164, 0.00065830362619350942834, 0.0021228688895969845957, 0.0066792918381076149537, 0.020201657448734186562, 0.057212961493873837338, 0.14427872595983071147, 0.29075824853504678158, 0.36564110074442957021, 0.13330127370825312072, 1.8509876543209877828e-13, 9.3684375000000001865e-13, 4.7411103515625013439e-12, 2.3989256927490236273e-11, 1.2134982390689849712e-10, 6.1360608361896876588e-10, 3.1008571052567070342e-09, 1.5656143551154886378e-08, 7.8940702751398238619e-08, 3.9721851623374797589e-07, 1.9925510408963188817e-06, 9.9478099413657914188e-06, 4.930161383220328208e-05, 0.00024154631749370003959, 0.0011617509224607792256, 0.0054175582232760775836, 0.023904363046940359239, 0.094270457100450164023, 0.27882003179675446392, 0.34581727191398570209, 1.2345679012345680828e-17, 9.3750000000000019611e-17, 7.1191406250000002335e-16, 5.4060974121093743207e-15, 4.1052552223205574609e-14, 3.1174281844496730813e-13, 2.3672970275664701427e-12, 1.7976661803082880424e-11, 1.3651027556716061272e-10, 1.0366249050881260814e-09, 7.8718703730129572237e-09, 5.9777015645067147174e-08, 4.5393171255472860584e-07, 3.4470439422124707377e-06, 2.6175989936175949598e-05, 0.00019877392357783613304, 0.0015094394821691928168, 0.011462306067722308894, 0.084705097520176186876, 0.41086218643981919918, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00077892972719669731786, 0.083370713567239546071, ]).reshape((20, 6, ), order="F")
--END TEST CASE--
--BEGIN TEST CASE--
degree=5
df=None
intercept=FALSE
Boundary.knots=np.array([0, 3000, ])
knots=np.array([10, 100, 1000, ])
output=np.array([0.40055561099999992258, 0.53723465540624992798, 0.68096479632714856933, 0.79404388738027953387, 0.82432650645400162848, 0.74772850370839760714, 0.60695411066016569102, 0.43541006443525137604, 0.25280073560290239332, 0.098204648213291390046, 0.015109611834895182725, 4.9867660859134372328e-05, 0, 0, 0, 0, 0, 0, 0, 0, 0.0089449327889999999397, 0.01902900664771874778, 0.039353322248692389207, 0.078015642723590794549, 0.14535820079903546964, 0.24864829948443537, 0.38546024005089984943, 0.54588573189455047441, 0.70480915137930777448, 0.81344804238272472308, 0.81717844525199323513, 0.71389962178539645432, 0.56021095018500233209, 0.38030131620961826755, 0.19975862317934431345, 0.062981619616186521049, 0.0053407260835513798575, 7.8059373845321992395e-10, 0, 0, 9.4545814444444459148e-06, 3.1017290343749996553e-05, 0.00010028811495410157556, 0.00031713361145809936415, 0.00096922483761113851879, 0.0028119072707210087531, 0.0075647083349296134064, 0.01861553313384527869, 0.042046394339120052308, 0.087124325583113268467, 0.16371060089498334911, 0.27411331174625735985, 0.40781324509205862938, 0.54286075051770132927, 0.63352683070691229172, 0.61289306201898841042, 0.44453962951650222157, 0.21269241367644950436, 0.052346279846925344859, 0.0018876298723944570646, 1.6294444444444447802e-09, 8.1548437499999982349e-09, 4.0568422851562507446e-08, 1.9994451278686527012e-07, 9.7096128099918361425e-07, 4.602189952521174856e-06, 2.0919648984453888767e-05, 8.8510989072612499115e-05, 0.0003426071500521665281, 0.0012159913347531664096, 0.0039613824662355322237, 0.011728070771626651625, 0.030981021316488138728, 0.072628957837475555115, 0.15091202659488145432, 0.27128094978421118944, 0.39491276471324787689, 0.40566968711467132902, 0.22597501897826155481, 0.025700635234072819607, 1.1111111111111111028e-13, 8.4374999999999991361e-13, 6.4072265625000016033e-12, 4.8654876708984379097e-11, 3.694729700088501736e-10, 2.8056853660047051398e-09, 2.1305018189503776537e-08, 1.5953977978988917004e-07, 1.1111370996233737328e-06, 6.9846696024166045466e-06, 3.9856249948076781855e-05, 0.00020802815256623063945, 0.00098445759075916142167, 0.0041227618062229087065, 0.015186511992247679614, 0.04905127538388008579, 0.1347633037922978394, 0.28642046903795781443, 0.37096041450757122337, 0.1373866644432741313, 0, 0, 0, 0, 0, 0, 2.1835286481805527197e-15, 7.5005881261891668233e-12, 3.9151793493690686266e-10, 7.8165151445884371071e-09, 1.0330194469679949402e-07, 1.0998832941312460716e-06, 1.0325449816590390154e-05, 8.6094485881001237893e-05, 0.00061191613127527461416, 0.0037238993946882535668, 0.01960252770274439893, 0.08667615291637777164, 0.27442290749348607903, 0.35255941616703945218, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.6587525069769108936e-10, 1.1914310104321792976e-07, 4.09139533897260065e-06, 6.9193802045574309841e-05, 0.00084104819165634655664, 0.0085412764739499439509, 0.075516449446559150149, 0.39909494071597967357, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.00077892972719669731786, 0.083370713567239546071, ]).reshape((20, 8, ), order="F")
--END TEST CASE--
"""
R_bs_num_tests = 72
