PIL/.dylibs/libXau.6.dylib,sha256=1yd4GRoVEYTzwrq-jireDLR8Cl7OD9HlJvtJzttObnE,53888
PIL/.dylibs/libbrotlicommon.1.1.0.dylib,sha256=Gece3ujludz9K5IRMDOTYKpZJdySGoz0SPeV0wyMgHQ,185680
PIL/.dylibs/libbrotlidec.1.1.0.dylib,sha256=ef6fu-dykiO6JZutU5QHTV99bqPwOzLAd6X52tfaUT8,88512
PIL/.dylibs/libfreetype.6.dylib,sha256=2jgBklKy6DZhoRt94ChcimyRHT3t1Ew2V9Z56-ACiy0,1115152
PIL/.dylibs/libharfbuzz.0.dylib,sha256=1LvFRGzDEHMy1KXzpHUBU290Gs93KPuzMeXeEsjH5gw,1385408
PIL/.dylibs/libjpeg.62.4.0.dylib,sha256=8Vpoya5HoVA5W8Wlx9aykq_dVtZbOjwdFsFF8E25_tU,731680
PIL/.dylibs/liblcms2.2.dylib,sha256=EXiby-PWp-3INwmrii0R4jAu82N0VIuBYG1T2gdVpig,526912
PIL/.dylibs/liblzma.5.dylib,sha256=bGjvqDPbJ387VcsUYLjGA6JTeXot6sF8ADS_ewNG_YI,310176
PIL/.dylibs/libopenjp2.2.5.3.dylib,sha256=TGT27-HXVYhCJR8ONdQ78UN0vRyritQZsP-lPXXWobM,636848
PIL/.dylibs/libpng16.16.dylib,sha256=-vOIyHJsA2qFVeZSxjp3iOImvtOQd6Jxw6O8UGPbqTY,312864
PIL/.dylibs/libsharpyuv.0.dylib,sha256=aE9pWnThghSevm9M3TaWzgw29A5QBcOUK2XShqTWdMU,70432
PIL/.dylibs/libtiff.6.dylib,sha256=AcW22LbH4aU_2509hmbm7l7mQbk8DqAwMRtllrr3FhI,756832
PIL/.dylibs/libwebp.7.dylib,sha256=s2Ty8bChFDsa3byuOSI7K6qwOlvdtAEl-SO5gt35wZU,674816
PIL/.dylibs/libwebpdemux.2.dylib,sha256=gGPq6aGIk335JQH5Nf_JeGW9XMHexP4GsRQLfRC8INE,53744
PIL/.dylibs/libwebpmux.3.dylib,sha256=EBEuyUtsK78QLQT8PwtOxbHiwMIhTJoxBpm_OV0QIds,90416
PIL/.dylibs/libxcb.1.1.0.dylib,sha256=kAxt3Hkbtl5fTSOewLr6L2eboTz44tOKb8kn2ZCKeFU,262528
PIL/.dylibs/libz.1.3.1.zlib-ng.dylib,sha256=9KxKLRi27KU9QbOdSpWaMUKsMoj3QDyheQqqtyEx83Q,196176
PIL/BdfFontFile.py,sha256=Hnlsd7gTtt7gaY1ilXHP-co5ehkBQ8xml942d06MEno,3477
PIL/BlpImagePlugin.py,sha256=7Ye8a8ojr4sW0a9bp6oYeWXsD1zkCIRHo6UqENlcOpU,16683
PIL/BmpImagePlugin.py,sha256=3li_kaQt9Q6-xvIFQ6tP_g_UkpbOadqdKDNlqBzT9Fg,19758
PIL/BufrStubImagePlugin.py,sha256=PcwNafLqxrQ_RTsz7hAqfpLWcQHT-kXKYPLaB46ZtUA,1753
PIL/ContainerIO.py,sha256=wkBqL2GDAb5fh3wrtfTGUfqioJipCl-lg2GxbjQrTZw,4604
PIL/CurImagePlugin.py,sha256=xTou-ULQPpI5lVdqpQ2-4Pdjnh7j1GddH-K22DQbdk0,1792
PIL/DcxImagePlugin.py,sha256=K70qkwz-e3nkgjftD5pIElMcU2klHWFTKbINlwgeQ24,2034
PIL/DdsImagePlugin.py,sha256=vv1dNj9T67YmoiBcKyLeDcMDv42WWdOXSDXg7THhA7s,16938
PIL/EpsImagePlugin.py,sha256=a8ErgTj7iJcPkzT1Iu7Dclkm-ms9WuQ3h0Gca505mh0,16365
PIL/ExifTags.py,sha256=zW6kVikCosiyoCo7J7R62evD3hoxjKPchnVh8po7CZc,9931
PIL/FitsImagePlugin.py,sha256=QVW0dCJpPCrV2N6qSHVS9nXpjYPHnbHWxSC88LcYDN0,4639
PIL/FliImagePlugin.py,sha256=pf-F5OmW9ehUQGUcZDcjlQMdJBxdjEC0_1VprHActeM,4675
PIL/FontFile.py,sha256=St7MxO5Q-oakCLWn3ZrgrtaT3wSsmAarxm8AU-G8Moc,3577
PIL/FpxImagePlugin.py,sha256=OAEpvvnalEIU2KeyXX7S-LEay_qyQNKu2dN76_xUoXk,7288
PIL/FtexImagePlugin.py,sha256=Eh5ZFg-js6TjVqTe5EUehRzsTKMPpr2Hs66kXs8pgw4,3527
PIL/GbrImagePlugin.py,sha256=5t0UfLubTPQcuDDbafwC78OLR7IsD5hjpvhUZ5g8z4A,3006
PIL/GdImageFile.py,sha256=sBT3em6cOKea3atNYE8u4L0ugaFlH6zcmt_7JHOOCYw,2802
PIL/GifImagePlugin.py,sha256=mRuxEiCPGp_CGet7DgXbWagA_JtPV-gdEpWxN_wLZCs,41456
PIL/GimpGradientFile.py,sha256=ABNhtD50Gv82Dn1VxbdgfSIz3Q2_nPiv_zDquOYyVAw,3898
PIL/GimpPaletteFile.py,sha256=mK8RqdS7Ae9W7gZ7NB7MkyzuOqhC6Z09_OsLkxCJoak,1427
PIL/GribStubImagePlugin.py,sha256=hzsipSut7wvQ14P5fx7mkGtLj2TWKZk7OwSiFstB194,1747
PIL/Hdf5StubImagePlugin.py,sha256=6bSeB8RJaWqdU3-xwnJIWtGBZjpM0QnpcM8974UWN90,1750
PIL/IcnsImagePlugin.py,sha256=mRXkUrNlnKB8vyW9UphHJ4JefgIrpzIqaUamf76uPec,12953
PIL/IcoImagePlugin.py,sha256=-6GillVbyW9nWlC83IKV7F-99O2aF01U7B1EuxGZpgY,12468
PIL/ImImagePlugin.py,sha256=HIbI1-XpN2RxNyrmMjFB9RKyb9GchBGaFlokk1DPYok,11438
PIL/Image.py,sha256=S3kgQ2R7nEP9b5ljsr3jbV-ybq-8TwZdSpSJ9LU2X0Y,146129
PIL/ImageChops.py,sha256=GEjlymcoDtA5OOeIxQVIX96BD-s6AXhb7TmSLYn2tUg,7946
PIL/ImageCms.py,sha256=wpVg1Kmp5WfeCNbEfGUCZsjcWVreg3HZqMHyTttlz1s,42010
PIL/ImageColor.py,sha256=IGA9C2umeED_EzS2Cvj6KsU0VutC9RstWIYPe8uDsVk,9441
PIL/ImageDraw.py,sha256=7TZ0miXREA8vFh0yCCAy3k0olUfYz8erDjM4-AH586o,42275
PIL/ImageDraw2.py,sha256=pdVMW7bVw3KwhXvRZh28Md4y-2xFfuo5fHcDnaYqVK4,7227
PIL/ImageEnhance.py,sha256=4Elhz_lyyxLmx0GkSHrwOAmNJ2TkqVQPHejzGihZUMI,3627
PIL/ImageFile.py,sha256=3Rkbo6XqFlg2QgbpVSf5jS8K0xps8-3j1w0LIAAZwKw,26125
PIL/ImageFilter.py,sha256=X3a-7xf1loq3j_MFDha0nAglsHImezBV8D4C0mMntEE,18710
PIL/ImageFont.py,sha256=LLt1wvLhLfAND_XruWyykRP3InLsyyCpXdwH4a9XQtA,64261
PIL/ImageGrab.py,sha256=-9RS8qP6nark5RW31IrRXlrPNrTno-gRJb0zOujyi48,6002
PIL/ImageMath.py,sha256=qDVyqP24n4FnCgJRgW_DVcRFoTdZFJLQd5qxAZS4EG4,11942
PIL/ImageMode.py,sha256=5yOxODAZ7jG03DsUFrt7eQayTtIpWPgvfyhlXDWwcv8,2681
PIL/ImageMorph.py,sha256=TowXnk1Q2wX9AXVBDWRRQhCfAbFOUWGMo00vq4yn-fU,8563
PIL/ImageOps.py,sha256=g68uCpv-BJIvZr2vHUEBZG98WRJDhEOj3jEYKurOdco,25091
PIL/ImagePalette.py,sha256=wTokkN4dylYRAzxirCmh6M_GyyqbkDazwfX2tEoohCs,9002
PIL/ImagePath.py,sha256=5yUG5XCUil1KKTTA_8PgGhcmg-mnue-GK0FwTBlhjw4,371
PIL/ImageQt.py,sha256=maxKjoYomf3nqO9dWhg9kXGqg0eRz5erAa1yT2_d9oo,6834
PIL/ImageSequence.py,sha256=gx2EvywPBEjxNJujCqdpbfAm2BpyNV2_f1IaO3niubw,2200
PIL/ImageShow.py,sha256=LBGhPR3k5Z20S7vDyCsL0BftIX5tvTvCd5xdCvA7lTc,9993
PIL/ImageStat.py,sha256=S43FZ89r_u4hKCj59lVuWpyVJfhbUy3igXkp9DwaMgM,5325
PIL/ImageTk.py,sha256=JuzOgUMKiAhR8JAYCSY1Il3iwQ8Hx-vwC4ng_KRKfCQ,8997
PIL/ImageTransform.py,sha256=okpZipXf2u7tDB3dticLozrOKI8QNIsniCq_J4CxQC0,3886
PIL/ImageWin.py,sha256=LT05w8_vTfRrC3n9S9pM0TNbXrzZLEJHlCJil7Xv80k,8085
PIL/ImtImagePlugin.py,sha256=JZnVN1bWNiptIrDmiUQYMJQL_aduaGrSCEjRIHFuoEA,2665
PIL/IptcImagePlugin.py,sha256=zMOEYveSc8ph1WdJtC-tUJEueDcInpVUviCcnqKXq0Q,6669
PIL/Jpeg2KImagePlugin.py,sha256=Zv0PhFk5dx0c1H85__4PWyDC-xHjjnb9h3iIe32KtRU,13885
PIL/JpegImagePlugin.py,sha256=tOi0VgbxWSQqChM0ZBSya05bH4FJHz8F1kT_6vRHUoY,31800
PIL/JpegPresets.py,sha256=lnqWHo4DLIHIulcdHp0NJ7CWexHt8T3w51kIKlLfkIA,12379
PIL/McIdasImagePlugin.py,sha256=G_sNQybucqISdmU-XGCtdIcm4jZTI59tcSz96WiUwDI,1938
PIL/MicImagePlugin.py,sha256=8EqZ-Vm9FK23XB-5thR3dWEcq4j3XbMG961-ILRnC0g,2680
PIL/MpegImagePlugin.py,sha256=AplKMsPC9Y9ExJfqTE_xlm8TW-CSx578NGQWyVcmIiQ,2100
PIL/MpoImagePlugin.py,sha256=QVkZnrOGAlPCARpraeNSS6Q-ymQXfQGKAUAfRWTDZMA,6220
PIL/MspImagePlugin.py,sha256=VCFo9otTuMvH_xnWu43Cm1gn5Lv0-C5utBOKkxo1JB8,5882
PIL/PSDraw.py,sha256=3hY8wDQamJr5X5dS8pwQ9eUMJAV835of7aX3t8kM5Q8,6909
PIL/PaletteFile.py,sha256=rC4YrlwwpJtl7RdPDnfl21HR4Vge3cAUw5Z6zicBqIk,1211
PIL/PalmImagePlugin.py,sha256=Lz2yNR9099-cjTs4NY-0XvHxxCDBSYJkqXJltcZkNXQ,9351
PIL/PcdImagePlugin.py,sha256=8LEZLY4sjRfsL5mQQF_V9YkoiQlrXoD1dRpzM1CtcWg,1592
PIL/PcfFontFile.py,sha256=NPZQ0XkbGB8uTlGqgmIPGkwuLMYBdykDeVuvFgIC7JU,7147
PIL/PcxImagePlugin.py,sha256=egtz8QOKgBGIXxLdUxd8CfD3vBVxRcWjkt6P9HtUpSY,6247
PIL/PdfImagePlugin.py,sha256=AbJA2f4qzH8G1olfmk18SzQlcx3WsipUYDc5bcR8Wvk,9349
PIL/PdfParser.py,sha256=0p4yxf90wHEx1jDRnjpKxjwfDqUYO463kaYS31PJpYY,37980
PIL/PixarImagePlugin.py,sha256=MZ3iR3IwjVKRIRs2CSl1xExkPizhYoAFNBGeufY0fLc,1753
PIL/PngImagePlugin.py,sha256=OWYk88lLD3eBYiXQf6FBLBjLgCwggK5zukTBYhGHL-w,50861
PIL/PpmImagePlugin.py,sha256=m2PDVO97GAn5kISON3-PJENWU3WZOiwRbPjiUp_rK0M,12354
PIL/PsdImagePlugin.py,sha256=5g-l_HrIWMG7xSAb4ofhgKVhsnK2yqh6ee9xE-Z1620,8621
PIL/QoiImagePlugin.py,sha256=xZVSfZVW9nVxjQjNDcOB6q7F0H1Iq--vOr_irCp65w0,4183
PIL/SgiImagePlugin.py,sha256=wjO3mgTO7AYC2Bs6RJBEKafm49wgFkCXZuVoBD6UWxc,6732
PIL/SpiderImagePlugin.py,sha256=a9S-wC7lckgN5x412LhPuCvpwg5kz9VaISDbAewpcV4,10133
PIL/SunImagePlugin.py,sha256=Hdxkhk0pxpBGxYhPJfCDLwsYcO1KjxjtplNMFYibIvk,4589
PIL/TarIO.py,sha256=uQ5Zh47x67H9fq8iGLSeCfk22i0E7Ae06fVC2bf1LcU,1376
PIL/TgaImagePlugin.py,sha256=2vDsFTcBUBHw1V80wpVv4tgpLDbPr6yVHi6Fvaqf0HY,6980
PIL/TiffImagePlugin.py,sha256=OuJZgFdoUnGvZ-NYZbhLuQ7VBz9UqeQSPsr2OGoSSqo,83398
PIL/TiffTags.py,sha256=-gbXLZ5rlHD6crwtY6TkafDm2tamlc5v8e7FjS8PcIg,17082
PIL/WalImageFile.py,sha256=Lfuq_WZ_V_onwucfUc6GWfvY7z_K4s-5EdaQGu_2DD4,5704
PIL/WebPImagePlugin.py,sha256=cewKnQP9W43Im-mG6htOV26w6mS1KuNHjAyuuGETGjQ,10061
PIL/WmfImagePlugin.py,sha256=XbuT349Oa-9JNil6wB-gMMEQkyyMTvdQ1POxDzt3UPk,5137
PIL/XVThumbImagePlugin.py,sha256=BoEiffKzyGEYnjZAKCICSCF7En7SaYHt77mqTW6R-qs,2110
PIL/XbmImagePlugin.py,sha256=dzrVoHrTR41zGlNCuy2U9za2WbWuR1G4S_JPA33wtJk,2664
PIL/XpmImagePlugin.py,sha256=ioqboXOmeB3LBqe6ACECiKYbISM4VZmv8DPy148kR3U,3226
PIL/__init__.py,sha256=fJUwPGhI8_mcB8jNWD-hUw7MiMJyWgqVX_nFtzIj1Zs,2008
PIL/__main__.py,sha256=Lpj4vef8mI7jA1sRCUAoVYaeePD_Uc898xF5c7XLx1A,133
PIL/__pycache__/BdfFontFile.cpython-39.pyc,,
PIL/__pycache__/BlpImagePlugin.cpython-39.pyc,,
PIL/__pycache__/BmpImagePlugin.cpython-39.pyc,,
PIL/__pycache__/BufrStubImagePlugin.cpython-39.pyc,,
PIL/__pycache__/ContainerIO.cpython-39.pyc,,
PIL/__pycache__/CurImagePlugin.cpython-39.pyc,,
PIL/__pycache__/DcxImagePlugin.cpython-39.pyc,,
PIL/__pycache__/DdsImagePlugin.cpython-39.pyc,,
PIL/__pycache__/EpsImagePlugin.cpython-39.pyc,,
PIL/__pycache__/ExifTags.cpython-39.pyc,,
PIL/__pycache__/FitsImagePlugin.cpython-39.pyc,,
PIL/__pycache__/FliImagePlugin.cpython-39.pyc,,
PIL/__pycache__/FontFile.cpython-39.pyc,,
PIL/__pycache__/FpxImagePlugin.cpython-39.pyc,,
PIL/__pycache__/FtexImagePlugin.cpython-39.pyc,,
PIL/__pycache__/GbrImagePlugin.cpython-39.pyc,,
PIL/__pycache__/GdImageFile.cpython-39.pyc,,
PIL/__pycache__/GifImagePlugin.cpython-39.pyc,,
PIL/__pycache__/GimpGradientFile.cpython-39.pyc,,
PIL/__pycache__/GimpPaletteFile.cpython-39.pyc,,
PIL/__pycache__/GribStubImagePlugin.cpython-39.pyc,,
PIL/__pycache__/Hdf5StubImagePlugin.cpython-39.pyc,,
PIL/__pycache__/IcnsImagePlugin.cpython-39.pyc,,
PIL/__pycache__/IcoImagePlugin.cpython-39.pyc,,
PIL/__pycache__/ImImagePlugin.cpython-39.pyc,,
PIL/__pycache__/Image.cpython-39.pyc,,
PIL/__pycache__/ImageChops.cpython-39.pyc,,
PIL/__pycache__/ImageCms.cpython-39.pyc,,
PIL/__pycache__/ImageColor.cpython-39.pyc,,
PIL/__pycache__/ImageDraw.cpython-39.pyc,,
PIL/__pycache__/ImageDraw2.cpython-39.pyc,,
PIL/__pycache__/ImageEnhance.cpython-39.pyc,,
PIL/__pycache__/ImageFile.cpython-39.pyc,,
PIL/__pycache__/ImageFilter.cpython-39.pyc,,
PIL/__pycache__/ImageFont.cpython-39.pyc,,
PIL/__pycache__/ImageGrab.cpython-39.pyc,,
PIL/__pycache__/ImageMath.cpython-39.pyc,,
PIL/__pycache__/ImageMode.cpython-39.pyc,,
PIL/__pycache__/ImageMorph.cpython-39.pyc,,
PIL/__pycache__/ImageOps.cpython-39.pyc,,
PIL/__pycache__/ImagePalette.cpython-39.pyc,,
PIL/__pycache__/ImagePath.cpython-39.pyc,,
PIL/__pycache__/ImageQt.cpython-39.pyc,,
PIL/__pycache__/ImageSequence.cpython-39.pyc,,
PIL/__pycache__/ImageShow.cpython-39.pyc,,
PIL/__pycache__/ImageStat.cpython-39.pyc,,
PIL/__pycache__/ImageTk.cpython-39.pyc,,
PIL/__pycache__/ImageTransform.cpython-39.pyc,,
PIL/__pycache__/ImageWin.cpython-39.pyc,,
PIL/__pycache__/ImtImagePlugin.cpython-39.pyc,,
PIL/__pycache__/IptcImagePlugin.cpython-39.pyc,,
PIL/__pycache__/Jpeg2KImagePlugin.cpython-39.pyc,,
PIL/__pycache__/JpegImagePlugin.cpython-39.pyc,,
PIL/__pycache__/JpegPresets.cpython-39.pyc,,
PIL/__pycache__/McIdasImagePlugin.cpython-39.pyc,,
PIL/__pycache__/MicImagePlugin.cpython-39.pyc,,
PIL/__pycache__/MpegImagePlugin.cpython-39.pyc,,
PIL/__pycache__/MpoImagePlugin.cpython-39.pyc,,
PIL/__pycache__/MspImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PSDraw.cpython-39.pyc,,
PIL/__pycache__/PaletteFile.cpython-39.pyc,,
PIL/__pycache__/PalmImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PcdImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PcfFontFile.cpython-39.pyc,,
PIL/__pycache__/PcxImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PdfImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PdfParser.cpython-39.pyc,,
PIL/__pycache__/PixarImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PngImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PpmImagePlugin.cpython-39.pyc,,
PIL/__pycache__/PsdImagePlugin.cpython-39.pyc,,
PIL/__pycache__/QoiImagePlugin.cpython-39.pyc,,
PIL/__pycache__/SgiImagePlugin.cpython-39.pyc,,
PIL/__pycache__/SpiderImagePlugin.cpython-39.pyc,,
PIL/__pycache__/SunImagePlugin.cpython-39.pyc,,
PIL/__pycache__/TarIO.cpython-39.pyc,,
PIL/__pycache__/TgaImagePlugin.cpython-39.pyc,,
PIL/__pycache__/TiffImagePlugin.cpython-39.pyc,,
PIL/__pycache__/TiffTags.cpython-39.pyc,,
PIL/__pycache__/WalImageFile.cpython-39.pyc,,
PIL/__pycache__/WebPImagePlugin.cpython-39.pyc,,
PIL/__pycache__/WmfImagePlugin.cpython-39.pyc,,
PIL/__pycache__/XVThumbImagePlugin.cpython-39.pyc,,
PIL/__pycache__/XbmImagePlugin.cpython-39.pyc,,
PIL/__pycache__/XpmImagePlugin.cpython-39.pyc,,
PIL/__pycache__/__init__.cpython-39.pyc,,
PIL/__pycache__/__main__.cpython-39.pyc,,
PIL/__pycache__/_binary.cpython-39.pyc,,
PIL/__pycache__/_deprecate.cpython-39.pyc,,
PIL/__pycache__/_tkinter_finder.cpython-39.pyc,,
PIL/__pycache__/_typing.cpython-39.pyc,,
PIL/__pycache__/_util.cpython-39.pyc,,
PIL/__pycache__/_version.cpython-39.pyc,,
PIL/__pycache__/features.cpython-39.pyc,,
PIL/__pycache__/report.cpython-39.pyc,,
PIL/_binary.py,sha256=pcM6AL04GxgmGeLfcH1V1BZHENwIrQH0uxhJ7r0HIL0,2550
PIL/_deprecate.py,sha256=SLU2p8O9ImHYHsD4VFGKLTkewh_Eda0axfIWUCnkKSg,1936
PIL/_imaging.cpython-39-darwin.so,sha256=M3vshUgoENOPiQhQhmy8R6GzVZI_NYqWf71sDKHQ7Y0,573648
PIL/_imaging.pyi,sha256=StMbXUZS32AegATP1sUHfs5P05A3TD_BiQKsDHQBW40,868
PIL/_imagingcms.cpython-39-darwin.so,sha256=BzjMlbAwvlRSysU2DPsGzMM8tFiPUxVkCo4_2uVqXsw,64832
PIL/_imagingcms.pyi,sha256=brpjxRoiY_2ItyfTrjhKeGEsExe4GPG-25q9AQP8Jp8,4389
PIL/_imagingft.cpython-39-darwin.so,sha256=_cdnBbw4gdJBRdsQ7M4jnjElZ3x2KhWt7bOolqtPHZ4,100688
PIL/_imagingft.pyi,sha256=62nD4AzNDHddKXgcxblOrxKwu8w7TJIHNM-mqC2fue0,1789
PIL/_imagingmath.cpython-39-darwin.so,sha256=NvTHf2t_eCw7VO_rttKGCHbOcuZkJU-vj_IFCurbANY,54976
PIL/_imagingmath.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/_imagingmorph.cpython-39-darwin.so,sha256=3o-ZTyfpT_v4aLOiwG1Ao7sRYNncAJ6nXNV8yGIy94o,34480
PIL/_imagingmorph.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/_imagingtk.cpython-39-darwin.so,sha256=8fxz2SiD4fw0PcebeDkLZyv0vGviGwC_vs13UdVfIp0,35984
PIL/_imagingtk.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/_tkinter_finder.py,sha256=CECvYrzWNc7BuzzR_mWZZKjPdADg6iRG8ilJToyjD3w,540
PIL/_typing.py,sha256=5nIQp0No7js6KhThjafVIrrddGbd_6EGvrj4BzElP-Q,1244
PIL/_util.py,sha256=E76J1WLAe6Xg5yNWYztQwYzxUT_sR_VQxFJu7IZ3S3k,635
PIL/_version.py,sha256=huIl0a2frkcR-Wem-VhDvWm-Vl4arrQvHgkDSBYVuTs,87
PIL/_webp.cpython-39-darwin.so,sha256=9NExInkV6BP0OhJsCb6HUHmcclIOOx0Q4gBHvoDYkH4,59056
PIL/_webp.pyi,sha256=3fBxcSppJr6EOEcUojvflG3Eegg7lv2Qp0dNQQILrP4,63
PIL/features.py,sha256=rwlbHZYj_OM2-qhscJczwnr3UnA1QlEJg6CBmGcKETA,11262
PIL/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
PIL/report.py,sha256=4JY6-IU7sH1RKuRbOvy1fUt0dAoi79FX4tYJN3p1DT0,100
pillow-11.1.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pillow-11.1.0.dist-info/LICENSE,sha256=9XbTk30hFh6sWbC294B6L18w_nfXKr_g1bTY6TmzEog,60071
pillow-11.1.0.dist-info/METADATA,sha256=UIIl--QvedtNB7RSKhfc5b6W1htrqa7JadqYx4sFRrA,9137
pillow-11.1.0.dist-info/RECORD,,
pillow-11.1.0.dist-info/WHEEL,sha256=dSeWg4j5rLGKC6-i_zqBp4HBEzl4m-NzaGXpHei_8J0,109
pillow-11.1.0.dist-info/top_level.txt,sha256=riZqrk-hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF-NY,4
pillow-11.1.0.dist-info/zip-safe,sha256=AbpHGcgLb-kRsJGnwFEktk7uzpZOCcBY74-YBdrKVGs,1
