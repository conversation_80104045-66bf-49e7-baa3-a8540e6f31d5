import _plotly_utils.basevalidators


class Y0Validator(_plotly_utils.basevalidators.AnyValidator):
    def __init__(self, plotly_name="y0", parent_name="bar", **kwargs):
        super(Y0Validator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            anim=kwargs.pop("anim", True),
            edit_type=kwargs.pop("edit_type", "calc+clearAxisTypes"),
            **kwargs,
        )
