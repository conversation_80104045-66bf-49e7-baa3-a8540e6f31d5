import sys
from typing import TYPE_CHECKING

if sys.version_info < (3, 7) or TYPE_CHECKING:
    from ._yside import Y<PERSON>Validator
    from ._ygap import <PERSON>gapValidator
    from ._yaxes import Ya<PERSON>Validator
    from ._xside import <PERSON>sideValidator
    from ._xgap import <PERSON><PERSON>p<PERSON><PERSON>da<PERSON>
    from ._xaxes import <PERSON>axesV<PERSON>da<PERSON>
    from ._subplots import SubplotsValida<PERSON>
    from ._rows import RowsValida<PERSON>
    from ._roworder import RoworderValidator
    from ._pattern import PatternValidator
    from ._domain import DomainValidator
    from ._columns import ColumnsValidator
else:
    from _plotly_utils.importers import relative_import

    __all__, __getattr__, __dir__ = relative_import(
        __name__,
        [],
        [
            "._yside.YsideValidator",
            "._ygap.YgapValidator",
            "._yaxes.YaxesValidator",
            "._xside.XsideValidator",
            "._xgap.XgapValidator",
            "._xaxes.XaxesValidator",
            "._subplots.SubplotsValidator",
            "._rows.RowsValidator",
            "._roworder.RoworderValidator",
            "._pattern.PatternValidator",
            "._domain.DomainValidator",
            "._columns.ColumnsValidator",
        ],
    )
