import _plotly_utils.basevalidators


class IconValidator(_plotly_utils.basevalidators.StringValidator):
    def __init__(
        self, plotly_name="icon", parent_name="layout.map.layer.symbol", **kwargs
    ):
        super(IconValidator, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            edit_type=kwargs.pop("edit_type", "plot"),
            **kwargs,
        )
