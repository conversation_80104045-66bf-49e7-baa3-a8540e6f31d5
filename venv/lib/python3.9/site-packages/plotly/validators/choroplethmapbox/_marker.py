import _plotly_utils.basevalidators


class MarkerValidator(_plotly_utils.basevalidators.CompoundValidator):
    def __init__(self, plotly_name="marker", parent_name="choroplethmapbox", **kwargs):
        super(<PERSON>erValida<PERSON>, self).__init__(
            plotly_name=plotly_name,
            parent_name=parent_name,
            data_class_str=kwargs.pop("data_class_str", "Marker"),
            data_docs=kwargs.pop(
                "data_docs",
                """
            line
                :class:`plotly.graph_objects.choroplethmapbox.m
                arker.Line` instance or dict with compatible
                properties
            opacity
                Sets the opacity of the locations.
            opacitysrc
                Sets the source reference on Chart Studio Cloud
                for `opacity`.
""",
            ),
            **kwargs,
        )
