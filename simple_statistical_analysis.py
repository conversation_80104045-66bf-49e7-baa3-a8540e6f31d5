"""
简化的ETF配对交易策略统计分析脚本
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import warnings

# 添加src目录到路径
sys.path.append('src')

from src.analysis.statistical_analyzer import ETFPairsStatisticalAnalyzer

warnings.filterwarnings('ignore')

def create_sample_data():
    """创建模拟数据"""
    print("创建模拟数据...")
    
    # 生成时间序列 (较小的数据集)
    dates = pd.date_range(start='2024-01-01', end='2024-01-05', freq='5min')
    n = len(dates)
    
    # 生成相关的价格序列
    np.random.seed(42)
    
    # ETF1价格
    returns1 = np.random.normal(0, 0.001, n)
    price1 = 100 * np.exp(np.cumsum(returns1))
    
    # ETF2价格 (与ETF1相关)
    beta = 0.8 + 0.1 * np.sin(np.arange(n) / 100)  # 时变beta
    noise = np.random.normal(0, 0.0005, n)
    price2 = beta * price1 + noise + 20
    
    data = pd.DataFrame({
        '518880': price1,
        '518850': price2
    }, index=dates)
    
    print(f"生成数据: {len(data)} 个数据点")
    return data

def main():
    print("=" * 60)
    print("ETF配对交易策略统计分析")
    print("=" * 60)
    
    # 1. 创建数据
    data = create_sample_data()
    
    # 2. 初始化分析器
    analyzer = ETFPairsStatisticalAnalyzer(output_dir="statistical_analysis_results")
    
    # 3. 加载数据
    analyzer.load_data(data)
    
    # 4. 计算价差序列
    analyzer.calculate_spread_series(window=20, std_dev_mult=0.5)
    
    # 5. 执行各项分析
    print("\n执行统计分析...")
    
    # 白噪声检验
    white_noise_results = analyzer.white_noise_tests()
    
    # 分布分析
    dist_results = analyzer.distribution_analysis()
    
    # 自相关分析
    autocorr_results = analyzer.autocorrelation_analysis()
    
    # 参数稳定性分析
    param_results = analyzer.parameter_stability_analysis()
    
    # 6. 生成优化建议
    suggestions = analyzer.generate_optimization_suggestions()
    
    # 7. 生成可视化（不显示）
    try:
        plot_path = analyzer.plot_analysis_results()
        print(f"可视化图表已保存: {plot_path}")
    except Exception as e:
        print(f"可视化生成失败: {str(e)}")
    
    # 8. 生成报告
    try:
        report_path = analyzer.generate_comprehensive_report()
        print(f"分析报告已保存: {report_path}")
    except Exception as e:
        print(f"报告生成失败: {str(e)}")
    
    # 9. 输出关键结果
    print("\n" + "=" * 60)
    print("关键分析结果")
    print("=" * 60)
    
    # 价差序列基本统计
    if 'descriptive_stats' in dist_results:
        stats = dist_results['descriptive_stats']
        print(f"\n价差序列统计:")
        print(f"- 样本数量: {stats['count']}")
        print(f"- 均值: {stats['mean']:.6f}")
        print(f"- 标准差: {stats['std']:.6f}")
        print(f"- 偏度: {stats['skewness']:.4f}")
        print(f"- 峰度: {stats['kurtosis']:.4f}")
    
    # 平稳性检验结果
    if 'adf' in white_noise_results and 'error' not in white_noise_results['adf']:
        adf = white_noise_results['adf']
        print(f"\n平稳性检验 (ADF):")
        print(f"- 统计量: {adf['statistic']:.4f}")
        print(f"- p值: {adf['p_value']:.4f}")
        print(f"- 平稳性: {'是' if adf['is_stationary'] else '否'}")
    
    # 正态性检验结果
    if 'normality_tests' in dist_results:
        norm_tests = dist_results['normality_tests']
        print(f"\n正态性检验:")
        for test_name, result in norm_tests.items():
            if 'error' not in result:
                print(f"- {test_name}: p值={result['p_value']:.4f}, "
                      f"正态分布={'是' if result['is_normal'] else '否'}")
    
    # 参数稳定性
    if 'beta_analysis' in param_results:
        beta_stats = param_results['beta_analysis'].get('statistics', {})
        print(f"\nBeta系数稳定性:")
        print(f"- 变异系数: {beta_stats.get('coefficient_of_variation', 0):.4f}")
        print(f"- 范围比: {beta_stats.get('range_ratio', 0):.4f}")
    
    # 优化建议
    print(f"\n优化建议:")
    if 'parameter_update_frequency' in suggestions:
        freq = suggestions['parameter_update_frequency']
        print(f"- 参数更新频率: {freq['suggested']} (当前: {freq['current']})")
        print(f"  理由: {freq['reasoning']}")
    
    if 'threshold_multiplier' in suggestions:
        threshold = suggestions['threshold_multiplier']
        print(f"- 阈值倍数: {threshold['suggested']} (当前: {threshold['current']})")
        print(f"  理由: {threshold['reasoning']}")
    
    print(f"\n分析完成！结果保存在: {analyzer.output_dir}")
    
    return analyzer.results

if __name__ == "__main__":
    results = main()
