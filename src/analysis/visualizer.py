import matplotlib
matplotlib.use('Agg')  # 设置后端为Agg，避免交互式窗口
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union
import os

class Visualizer:
    """回测结果可视化工具"""
    
    def __init__(self, results: Dict):
        """
        初始化可视化工具
        Args:
            results: 回测结果字典
        """
        self.results = results
        self.daily_returns = pd.Series(results['daily_returns'])
        self.equity_curve = pd.Series(results['equity_curve'])
        self.trades = results['trades']
        
        # 设置绘图风格
        plt.style.use('classic')
        sns.set_palette("husl")
    
    def plot_equity_curve(self, ax: Optional[plt.Axes] = None) -> plt.Axes:
        """
        绘制权益曲线
        Args:
            ax: matplotlib轴对象
        Returns:
            matplotlib轴对象
        """
        if ax is None:
            fig, ax = plt.subplots(figsize=(12, 6))
        
        # 获取价格数据以获取日期时间索引
        prices = self.results.get('prices', None)
        if prices is not None and len(prices) > 0:
            # 确保equity_curve和dates长度匹配
            if len(self.equity_curve) > len(prices.index):
                # 第一个值是初始资金，可能需要特殊处理
                dates = [prices.index[0] - pd.Timedelta(minutes=1)]  # 前一分钟作为初始时间点
                dates.extend(prices.index)
            else:
                dates = prices.index
            
            ax.plot(dates[:len(self.equity_curve)], self.equity_curve, color='blue')
        else:
            # 如果没有日期信息，退回到使用序列索引
            ax.plot(range(len(self.equity_curve)), self.equity_curve, color='blue')
        
        ax.set_title('Equity Curve')
        ax.set_xlabel('Date Time')
        ax.set_ylabel('Portfolio Value')
        ax.grid(True)
        
        # 格式化x轴日期
        plt.xticks(rotation=45)
        ax.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.gcf().autofmt_xdate()  # 自动调整日期标签以避免重叠
        
        return ax
    
    def plot_returns_distribution(self, ax: Optional[plt.Axes] = None) -> plt.Axes:
        """
        绘制收益分布图
        Args:
            ax: matplotlib轴对象
        Returns:
            matplotlib轴对象
        """
        if ax is None:
            fig, ax = plt.subplots(figsize=(12, 6))
        
        sns.histplot(self.daily_returns, kde=True, ax=ax)
        ax.set_title('Daily Returns Distribution')
        ax.set_xlabel('Return')
        ax.set_ylabel('Frequency')
        
        # 添加统计信息
        mean = np.mean(self.daily_returns)
        std = np.std(self.daily_returns)
        skew = pd.Series(self.daily_returns).skew()
        kurt = pd.Series(self.daily_returns).kurtosis()
        
        stats_text = f'Mean: {mean:.4f}\nStd: {std:.4f}\nSkew: {skew:.4f}\nKurt: {kurt:.4f}'
        ax.text(0.95, 0.95, stats_text,
                transform=ax.transAxes,
                verticalalignment='top',
                horizontalalignment='right',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
        
        return ax
    
    def plot_drawdown(self, ax: Optional[plt.Axes] = None) -> plt.Axes:
        """
        绘制回撤图
        Args:
            ax: matplotlib轴对象
        Returns:
            matplotlib轴对象
        """
        if ax is None:
            fig, ax = plt.subplots(figsize=(12, 6))
        
        # 计算回撤序列
        equity_curve = np.array(self.equity_curve)
        peak = np.maximum.accumulate(equity_curve)
        drawdown = (equity_curve - peak) / peak * 100
        
        # 获取价格数据以获取日期时间索引
        prices = self.results.get('prices', None)
        if prices is not None and len(prices) > 0:
            # 确保drawdown和dates长度匹配
            if len(drawdown) > len(prices.index):
                # 第一个值是初始资金，可能需要特殊处理
                dates = [prices.index[0] - pd.Timedelta(minutes=1)]  # 前一分钟作为初始时间点
                dates.extend(prices.index)
            else:
                dates = prices.index
            
            ax.fill_between(dates[:len(drawdown)], drawdown, 0, color='red', alpha=0.3)
            ax.plot(dates[:len(drawdown)], drawdown, color='red')
        else:
            # 如果没有日期信息，退回到使用序列索引
            ax.fill_between(range(len(drawdown)), drawdown, 0, color='red', alpha=0.3)
            ax.plot(range(len(drawdown)), drawdown, color='red')
        
        ax.set_title('Drawdown')
        ax.set_xlabel('Date Time')
        ax.set_ylabel('Drawdown (%)')
        ax.grid(True)
        
        # 格式化x轴日期
        plt.xticks(rotation=45)
        ax.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.gcf().autofmt_xdate()  # 自动调整日期标签以避免重叠
        
        return ax
    
    def plot_trade_analysis(self, ax: Optional[plt.Axes] = None) -> plt.Axes:
        """
        绘制交易分析图
        Args:
            ax: matplotlib轴对象
        Returns:
            matplotlib轴对象
        """
        if ax is None:
            fig, ax = plt.subplots(figsize=(12, 6))
        
        if not self.trades:
            ax.text(0.5, 0.5, 'No trades available',
                   horizontalalignment='center',
                   verticalalignment='center')
            return ax
        
        # 提取交易收益
        trade_returns = []
        dates = []
        colors = []
        
        for trade in self.trades:
            if trade['direction'] == 1:  # 买入
                trade_return = -trade['trade_value'] - trade['commission']
            else:  # 卖出
                trade_return = trade['trade_value'] - trade['commission']
            trade_returns.append(trade_return)
            dates.append(trade['datetime'])
            colors.append('g' if trade_return > 0 else 'r')
        
        # 绘制交易收益散点图，使用日期作为横轴
        ax.scatter(dates, trade_returns, c=colors, alpha=0.5)
        ax.axhline(y=0, color='black', linestyle='--')
        
        ax.set_title('Trade Returns')
        ax.set_xlabel('Date Time')
        ax.set_ylabel('Return')
        ax.grid(True)
        
        # 格式化x轴日期
        plt.xticks(rotation=45)
        ax.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.gcf().autofmt_xdate()  # 自动调整日期标签以避免重叠
        
        return ax
    
    def plot_etf_holdings(self, ax: Optional[plt.Axes] = None) -> plt.Axes:
        """
        绘制ETF1持仓份额变化图
        Args:
            ax: matplotlib轴对象
        Returns:
            matplotlib轴对象
        """
        if ax is None:
            fig, ax = plt.subplots(figsize=(12, 6))
            
        if not self.trades:
            ax.text(0.5, 0.5, 'No trades available',
                   horizontalalignment='center',
                   verticalalignment='center')
            return ax
            
        # 获取ETF1的代码（假设第一笔交易的symbol是ETF1）
        etf1_symbol = self.trades[0]['symbol']
        
        # 计算每个时间点的ETF1持仓
        holdings = []
        current_holdings = 0
        dates = []
        
        for trade in self.trades:
            if trade['symbol'] == etf1_symbol:
                current_holdings += trade['volume'] * trade['direction']
            holdings.append(current_holdings)
            dates.append(trade['datetime'])
        
        # 绘制持仓变化，使用日期作为横轴
        ax.plot(dates, holdings, color='blue')
        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        
        # 设置标题和标签
        ax.set_title(f'{etf1_symbol} Holdings Over Time')
        ax.set_xlabel('Date Time')
        ax.set_ylabel('Number of Shares')
        ax.grid(True)
        
        # 格式化x轴日期
        plt.xticks(rotation=45)
        ax.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.gcf().autofmt_xdate()  # 自动调整日期标签以避免重叠
        
        return ax
    
    def plot_price_ratio(self, ax: Optional[plt.Axes] = None) -> plt.Axes:
        """
        绘制两个ETF的价格比值和移动平均线时间序列图
        Args:
            ax: matplotlib轴对象
        Returns:
            matplotlib轴对象
        """
        if ax is None:
            fig, ax = plt.subplots(figsize=(12, 6))

        # 获取信号数据
        signals = self.results.get('signals', None)
        if signals is None:
            ax.text(0.5, 0.5, 'No signal data available',
                   horizontalalignment='center',
                   verticalalignment='center')
            return ax

        # 获取价格数据
        prices = self.results.get('prices', None)
        if prices is None or len(prices) == 0:
            ax.text(0.5, 0.5, 'No price data available',
                   horizontalalignment='center',
                   verticalalignment='center')
            return ax

        # 获取价格比率和MA数据
        price_ratio = signals['price_ratio']
        ma = signals['ma']
        
        # 使用日期时间作为横轴
        # 获取日期时间索引
        dates = price_ratio.index
        
        # 绘制价格比值（设置为绿色，透明度0.7）
        ax.plot(dates, price_ratio, label='Price Ratio', color='green', alpha=0.4)
        
        # 绘制移动平均线
        if ma is not None:
            ax.plot(dates, ma, label='Moving Average', color='blue', linestyle='-.')
        
        # 获取上下边界数据
        upper_band = signals.get('raw_upper', None)
        lower_band = signals.get('raw_lower', None)
        
        if upper_band is not None and lower_band is not None:
            # 填充上下边界之间的区域（设置为粉红色，透明度1）
            ax.fill_between(dates, upper_band, lower_band, 
                          color='pink', alpha=1, label='Trading Range')
        
        # 设置标题和标签
        symbol1 = prices.columns[0]
        symbol2 = prices.columns[1]
        ax.set_title(f'Price Ratio ({symbol1}/{symbol2}) and Moving Average')
        ax.set_xlabel('Date Time')
        ax.set_ylabel('Value')
        ax.grid(True)
        ax.legend()
        
        # 格式化x轴日期
        plt.xticks(rotation=45)
        ax.xaxis.set_major_formatter(plt.matplotlib.dates.DateFormatter('%Y-%m-%d %H:%M'))
        plt.gcf().autofmt_xdate()  # 自动调整日期标签以避免重叠
        
        return ax
    
    def plot_results(self, save_dir: Optional[str] = 'results') -> None:
        """
        绘制所有分析图表
        Args:
            save_dir: 保存目录
        """
        # 创建保存目录
        if save_dir:
            os.makedirs(save_dir, exist_ok=True)
        
        # 创建子图
        fig, axes = plt.subplots(3, 2, figsize=(20, 24))
        
        # 绘制各个图表
        self.plot_equity_curve(axes[0, 0])
        self.plot_returns_distribution(axes[0, 1])
        self.plot_drawdown(axes[1, 0])
        self.plot_trade_analysis(axes[1, 1])
        self.plot_etf_holdings(axes[2, 0])
        self.plot_price_ratio(axes[2, 1])  # 添加价格比值图
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        if save_dir:
            date = pd.Timestamp.now().strftime('%Y%m%d')
            plt.savefig(os.path.join(save_dir, f'backtest_analysis_{date}.png'),
                       dpi=300, bbox_inches='tight')
        
        plt.close() 