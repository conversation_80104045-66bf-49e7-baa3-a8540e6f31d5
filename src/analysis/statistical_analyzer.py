"""
ETF配对交易策略统计分析模块

本模块专门用于分析ETF配对交易策略的统计特性，包括：
1. 价差序列统计特性分析
2. 回归参数时变性分析
3. 交易信号的统计验证

主要功能：
- 白噪声检验（Ljung-Box检验、ADF平稳性检验）
- 分布特征分析（正态性检验、描述性统计）
- 序列相关性和聚集性分析
- 回归参数稳定性评估
- 参数更新频率优化建议
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Union
import warnings
from datetime import datetime
import os

# 统计检验相关库
from scipy import stats
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.stats.stattools import jarque_bera
from statsmodels.regression.linear_model import OLS
from statsmodels.regression.rolling import RollingOLS
# from arch.unitroot import PhillipsPerron  # 可选导入

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")

warnings.filterwarnings('ignore')


class ETFPairsStatisticalAnalyzer:
    """ETF配对交易策略统计分析器"""
    
    def __init__(self, output_dir: str = "statistical_analysis"):
        """
        初始化统计分析器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 存储分析结果
        self.results = {
            'spread_analysis': {},
            'parameter_analysis': {},
            'signal_analysis': {},
            'optimization_suggestions': {}
        }
        
        # 存储原始数据
        self.price_data = None
        self.signals_data = None
        self.spread_series = None
        self.beta_series = None
        self.std_error_series = None
        
    def load_data(self, price_data: pd.DataFrame, signals_data: pd.DataFrame = None):
        """
        加载价格数据和信号数据
        
        Args:
            price_data: 价格数据，包含两个ETF的价格
            signals_data: 信号数据，包含rolling regression结果
        """
        self.price_data = price_data.copy()
        if signals_data is not None:
            self.signals_data = signals_data.copy()
        
        print(f"数据加载完成:")
        print(f"价格数据: {len(self.price_data)} 行, {self.price_data.columns.tolist()}")
        if self.signals_data is not None:
            print(f"信号数据: {len(self.signals_data)} 行, {self.signals_data.columns.tolist()}")
    
    def calculate_spread_series(self, window: int = 40, std_dev_mult: float = 0.5):
        """
        计算价差序列 (y - beta*x)
        
        Args:
            window: rolling regression窗口大小
            std_dev_mult: 标准差倍数
        """
        if self.price_data is None or len(self.price_data.columns) != 2:
            raise ValueError("需要包含两个ETF价格的数据")
        
        etf1, etf2 = self.price_data.columns
        y = self.price_data[etf1]
        x = self.price_data[etf2]
        
        print(f"计算价差序列: {etf1} vs {etf2}")
        print(f"参数: window={window}, std_dev_mult={std_dev_mult}")
        
        # 执行rolling regression
        try:
            rolling_model = RollingOLS(y, x, window=window)
            rolling_results = rolling_model.fit()
            
            # 获取beta和标准误差
            beta_values = rolling_results.params[etf2]
            std_error_values = rolling_results.bse[etf2]
            
            # 计算价差序列 (residuals)
            spread_series = y - beta_values * x
            
            # 存储结果
            self.spread_series = spread_series.dropna()
            self.beta_series = beta_values.dropna()
            self.std_error_series = std_error_values.dropna()
            
            print(f"价差序列计算完成: {len(self.spread_series)} 个有效数据点")
            
            return self.spread_series
            
        except Exception as e:
            print(f"计算价差序列时出错: {str(e)}")
            raise
    
    def white_noise_tests(self) -> Dict:
        """
        对价差序列进行白噪声检验
        
        Returns:
            检验结果字典
        """
        if self.spread_series is None:
            raise ValueError("请先计算价差序列")
        
        print("\n=== 白噪声检验 ===")
        results = {}
        
        # 1. Ljung-Box检验 (序列相关性检验)
        print("1. Ljung-Box检验 (序列相关性)")
        try:
            # 检验不同滞后期
            lags_to_test = [1, 5, 10, 20]
            ljung_box_results = {}
            
            for lag in lags_to_test:
                if len(self.spread_series) > lag:
                    try:
                        lb_result = acorr_ljungbox(self.spread_series, lags=lag, return_df=False)

                        # 处理不同版本的statsmodels返回格式
                        if isinstance(lb_result, tuple) and len(lb_result) >= 2:
                            lb_stat, lb_pvalue = lb_result[0], lb_result[1]
                        else:
                            lb_stat, lb_pvalue = lb_result, lb_result

                        # 提取数值
                        if hasattr(lb_stat, 'iloc'):
                            stat_val = float(lb_stat.iloc[-1])
                            pval_val = float(lb_pvalue.iloc[-1])
                        elif hasattr(lb_stat, '__iter__') and not isinstance(lb_stat, str):
                            stat_val = float(lb_stat[-1]) if len(lb_stat) > 0 else float(lb_stat)
                            pval_val = float(lb_pvalue[-1]) if len(lb_pvalue) > 0 else float(lb_pvalue)
                        else:
                            stat_val = float(lb_stat)
                            pval_val = float(lb_pvalue)

                        ljung_box_results[f'lag_{lag}'] = {
                            'statistic': stat_val,
                            'p_value': pval_val,
                            'is_white_noise': pval_val > 0.05
                        }
                    except Exception as e:
                        print(f"   滞后{lag}期检验失败: {str(e)}")
                        continue
                    print(f"   滞后{lag}期: 统计量={ljung_box_results[f'lag_{lag}']['statistic']:.4f}, "
                          f"p值={ljung_box_results[f'lag_{lag}']['p_value']:.4f}, "
                          f"白噪声={'是' if ljung_box_results[f'lag_{lag}']['is_white_noise'] else '否'}")
            
            results['ljung_box'] = ljung_box_results
            
        except Exception as e:
            print(f"   Ljung-Box检验失败: {str(e)}")
            results['ljung_box'] = {'error': str(e)}
        
        # 2. ADF平稳性检验
        print("\n2. ADF平稳性检验")
        try:
            adf_result = adfuller(self.spread_series.dropna())
            results['adf'] = {
                'statistic': adf_result[0],
                'p_value': adf_result[1],
                'critical_values': adf_result[4],
                'is_stationary': adf_result[1] < 0.05
            }
            print(f"   ADF统计量: {adf_result[0]:.4f}")
            print(f"   p值: {adf_result[1]:.4f}")
            print(f"   平稳性: {'是' if adf_result[1] < 0.05 else '否'}")
            print(f"   临界值: {adf_result[4]}")
            
        except Exception as e:
            print(f"   ADF检验失败: {str(e)}")
            results['adf'] = {'error': str(e)}
        
        # 3. KPSS检验 (平稳性检验的补充)
        print("\n3. KPSS检验")
        try:
            kpss_result = kpss(self.spread_series.dropna())
            results['kpss'] = {
                'statistic': kpss_result[0],
                'p_value': kpss_result[1],
                'critical_values': kpss_result[3],
                'is_stationary': kpss_result[1] > 0.05  # KPSS的零假设是平稳
            }
            print(f"   KPSS统计量: {kpss_result[0]:.4f}")
            print(f"   p值: {kpss_result[1]:.4f}")
            print(f"   平稳性: {'是' if kpss_result[1] > 0.05 else '否'}")
            
        except Exception as e:
            print(f"   KPSS检验失败: {str(e)}")
            results['kpss'] = {'error': str(e)}
        
        self.results['spread_analysis']['white_noise_tests'] = results
        return results
    
    def distribution_analysis(self) -> Dict:
        """
        分析价差序列的分布特征
        
        Returns:
            分布分析结果
        """
        if self.spread_series is None:
            raise ValueError("请先计算价差序列")
        
        print("\n=== 价差序列分布特征分析 ===")
        results = {}
        
        # 1. 描述性统计
        print("1. 描述性统计")
        desc_stats = self.spread_series.describe()
        results['descriptive_stats'] = {
            'count': int(desc_stats['count']),
            'mean': float(desc_stats['mean']),
            'std': float(desc_stats['std']),
            'min': float(desc_stats['min']),
            'max': float(desc_stats['max']),
            'skewness': float(stats.skew(self.spread_series.dropna())),
            'kurtosis': float(stats.kurtosis(self.spread_series.dropna())),
            'median': float(desc_stats['50%']),
            'q25': float(desc_stats['25%']),
            'q75': float(desc_stats['75%'])
        }
        
        print(f"   样本数量: {results['descriptive_stats']['count']}")
        print(f"   均值: {results['descriptive_stats']['mean']:.6f}")
        print(f"   标准差: {results['descriptive_stats']['std']:.6f}")
        print(f"   偏度: {results['descriptive_stats']['skewness']:.4f}")
        print(f"   峰度: {results['descriptive_stats']['kurtosis']:.4f}")
        
        # 2. 正态性检验
        print("\n2. 正态性检验")
        normality_tests = {}
        
        # Shapiro-Wilk检验 (适用于小样本)
        if len(self.spread_series) <= 5000:
            try:
                sw_stat, sw_pvalue = stats.shapiro(self.spread_series.dropna())
                normality_tests['shapiro_wilk'] = {
                    'statistic': float(sw_stat),
                    'p_value': float(sw_pvalue),
                    'is_normal': sw_pvalue > 0.05
                }
                print(f"   Shapiro-Wilk: 统计量={sw_stat:.4f}, p值={sw_pvalue:.4f}, "
                      f"正态分布={'是' if sw_pvalue > 0.05 else '否'}")
            except Exception as e:
                print(f"   Shapiro-Wilk检验失败: {str(e)}")
        
        # Kolmogorov-Smirnov检验
        try:
            # 标准化数据
            standardized = (self.spread_series - self.spread_series.mean()) / self.spread_series.std()
            ks_stat, ks_pvalue = stats.kstest(standardized.dropna(), 'norm')
            normality_tests['kolmogorov_smirnov'] = {
                'statistic': float(ks_stat),
                'p_value': float(ks_pvalue),
                'is_normal': ks_pvalue > 0.05
            }
            print(f"   Kolmogorov-Smirnov: 统计量={ks_stat:.4f}, p值={ks_pvalue:.4f}, "
                  f"正态分布={'是' if ks_pvalue > 0.05 else '否'}")
        except Exception as e:
            print(f"   Kolmogorov-Smirnov检验失败: {str(e)}")
        
        # Jarque-Bera检验
        try:
            jb_stat, jb_pvalue = jarque_bera(self.spread_series.dropna())
            normality_tests['jarque_bera'] = {
                'statistic': float(jb_stat),
                'p_value': float(jb_pvalue),
                'is_normal': jb_pvalue > 0.05
            }
            print(f"   Jarque-Bera: 统计量={jb_stat:.4f}, p值={jb_pvalue:.4f}, "
                  f"正态分布={'是' if jb_pvalue > 0.05 else '否'}")
        except Exception as e:
            print(f"   Jarque-Bera检验失败: {str(e)}")
        
        results['normality_tests'] = normality_tests
        
        self.results['spread_analysis']['distribution_analysis'] = results
        return results

    def autocorrelation_analysis(self) -> Dict:
        """
        分析价差序列的自相关性和聚集性

        Returns:
            自相关分析结果
        """
        if self.spread_series is None:
            raise ValueError("请先计算价差序列")

        print("\n=== 价差序列自相关性分析 ===")
        results = {}

        # 1. 计算自相关函数
        print("1. 自相关函数分析")
        max_lags = min(50, len(self.spread_series) // 4)

        try:
            from statsmodels.tsa.stattools import acf, pacf

            # 自相关函数
            acf_result = acf(self.spread_series.dropna(),
                           nlags=max_lags,
                           alpha=0.05)
            if isinstance(acf_result, tuple):
                acf_values, acf_confint = acf_result
            else:
                acf_values = acf_result
                acf_confint = None

            # 偏自相关函数
            pacf_result = pacf(self.spread_series.dropna(),
                             nlags=max_lags,
                             alpha=0.05)
            if isinstance(pacf_result, tuple):
                pacf_values, pacf_confint = pacf_result
            else:
                pacf_values = pacf_result
                pacf_confint = None

            results['autocorrelation'] = {
                'acf_values': acf_values.tolist() if hasattr(acf_values, 'tolist') else list(acf_values),
                'acf_confint': acf_confint.tolist() if acf_confint is not None and hasattr(acf_confint, 'tolist') else None,
                'pacf_values': pacf_values.tolist() if hasattr(pacf_values, 'tolist') else list(pacf_values),
                'pacf_confint': pacf_confint.tolist() if pacf_confint is not None and hasattr(pacf_confint, 'tolist') else None,
                'max_lags': max_lags
            }

            # 检查显著的自相关
            significant_lags = []
            if acf_confint is not None:
                for i in range(1, len(acf_values)):
                    if abs(acf_values[i]) > abs(acf_confint[i][1] - acf_confint[i][0]) / 2:
                        significant_lags.append(i)
            else:
                # 如果没有置信区间，使用简单的阈值
                threshold = 2 / np.sqrt(len(self.spread_series))
                for i in range(1, len(acf_values)):
                    if abs(acf_values[i]) > threshold:
                        significant_lags.append(i)

            results['significant_autocorr_lags'] = significant_lags
            print(f"   显著自相关滞后期: {significant_lags[:10]}...")  # 只显示前10个

        except Exception as e:
            print(f"   自相关分析失败: {str(e)}")
            results['autocorrelation'] = {'error': str(e)}

        # 2. 聚集性分析 (ARCH效应检验)
        print("\n2. 聚集性分析 (ARCH效应)")
        try:
            from statsmodels.stats.diagnostic import het_arch

            # ARCH-LM检验
            arch_test = het_arch(self.spread_series.dropna(), nlags=5)
            results['arch_test'] = {
                'lm_statistic': float(arch_test[0]),
                'p_value': float(arch_test[1]),
                'f_statistic': float(arch_test[2]),
                'f_p_value': float(arch_test[3]),
                'has_arch_effect': arch_test[1] < 0.05
            }

            print(f"   ARCH-LM统计量: {arch_test[0]:.4f}")
            print(f"   p值: {arch_test[1]:.4f}")
            print(f"   ARCH效应: {'存在' if arch_test[1] < 0.05 else '不存在'}")

        except Exception as e:
            print(f"   ARCH效应检验失败: {str(e)}")
            results['arch_test'] = {'error': str(e)}

        # 3. 周期性分析
        print("\n3. 周期性模式分析")
        try:
            # 使用FFT分析频域特征
            from scipy.fft import fft, fftfreq

            spread_clean = self.spread_series.dropna()
            n = len(spread_clean)

            # 去除趋势
            detrended = spread_clean - np.linspace(spread_clean.iloc[0], spread_clean.iloc[-1], n)

            # FFT分析
            fft_values = fft(detrended.values)
            frequencies = fftfreq(n)

            # 找到主要频率成分
            power_spectrum = np.abs(fft_values) ** 2
            dominant_freq_idx = np.argsort(power_spectrum[1:n//2])[-5:]  # 前5个主要频率
            dominant_frequencies = frequencies[1:n//2][dominant_freq_idx]
            dominant_periods = 1 / np.abs(dominant_frequencies[dominant_frequencies != 0])

            results['periodicity'] = {
                'dominant_frequencies': dominant_frequencies.tolist(),
                'dominant_periods': dominant_periods.tolist(),
                'power_spectrum_peak_ratio': float(np.max(power_spectrum[1:]) / np.mean(power_spectrum[1:]))
            }

            print(f"   主要周期: {[f'{p:.1f}' for p in dominant_periods[:3]]} 个数据点")
            print(f"   频谱峰值比: {results['periodicity']['power_spectrum_peak_ratio']:.2f}")

        except Exception as e:
            print(f"   周期性分析失败: {str(e)}")
            results['periodicity'] = {'error': str(e)}

        self.results['spread_analysis']['autocorrelation_analysis'] = results
        return results

    def parameter_stability_analysis(self) -> Dict:
        """
        分析回归参数的时变性和稳定性

        Returns:
            参数稳定性分析结果
        """
        if self.beta_series is None or self.std_error_series is None:
            raise ValueError("请先计算价差序列以获得回归参数")

        print("\n=== 回归参数稳定性分析 ===")
        results = {}

        # 1. Beta系数分析
        print("1. Beta系数时变性分析")
        beta_clean = self.beta_series.dropna()

        beta_stats = {
            'mean': float(beta_clean.mean()),
            'std': float(beta_clean.std()),
            'min': float(beta_clean.min()),
            'max': float(beta_clean.max()),
            'coefficient_of_variation': float(beta_clean.std() / beta_clean.mean()),
            'range_ratio': float((beta_clean.max() - beta_clean.min()) / beta_clean.mean())
        }

        print(f"   Beta均值: {beta_stats['mean']:.4f}")
        print(f"   Beta标准差: {beta_stats['std']:.4f}")
        print(f"   变异系数: {beta_stats['coefficient_of_variation']:.4f}")
        print(f"   范围比: {beta_stats['range_ratio']:.4f}")

        # Beta平稳性检验
        try:
            beta_adf = adfuller(beta_clean)
            beta_stability = {
                'adf_statistic': beta_adf[0],
                'adf_p_value': beta_adf[1],
                'is_stationary': beta_adf[1] < 0.05
            }
            print(f"   Beta平稳性: {'平稳' if beta_adf[1] < 0.05 else '非平稳'} (p={beta_adf[1]:.4f})")
        except Exception as e:
            beta_stability = {'error': str(e)}
            print(f"   Beta平稳性检验失败: {str(e)}")

        results['beta_analysis'] = {
            'statistics': beta_stats,
            'stability': beta_stability
        }

        # 2. 标准误差分析
        print("\n2. 标准误差时变性分析")
        std_err_clean = self.std_error_series.dropna()

        std_err_stats = {
            'mean': float(std_err_clean.mean()),
            'std': float(std_err_clean.std()),
            'min': float(std_err_clean.min()),
            'max': float(std_err_clean.max()),
            'coefficient_of_variation': float(std_err_clean.std() / std_err_clean.mean()),
            'range_ratio': float((std_err_clean.max() - std_err_clean.min()) / std_err_clean.mean())
        }

        print(f"   标准误差均值: {std_err_stats['mean']:.6f}")
        print(f"   标准误差标准差: {std_err_stats['std']:.6f}")
        print(f"   变异系数: {std_err_stats['coefficient_of_variation']:.4f}")

        # 标准误差平稳性检验
        try:
            std_err_adf = adfuller(std_err_clean)
            std_err_stability = {
                'adf_statistic': std_err_adf[0],
                'adf_p_value': std_err_adf[1],
                'is_stationary': std_err_adf[1] < 0.05
            }
            print(f"   标准误差平稳性: {'平稳' if std_err_adf[1] < 0.05 else '非平稳'} (p={std_err_adf[1]:.4f})")
        except Exception as e:
            std_err_stability = {'error': str(e)}
            print(f"   标准误差平稳性检验失败: {str(e)}")

        results['std_error_analysis'] = {
            'statistics': std_err_stats,
            'stability': std_err_stability
        }

        # 3. 参数变化率分析
        print("\n3. 参数变化率分析")
        try:
            # Beta变化率
            beta_changes = beta_clean.diff().dropna()
            if len(beta_changes) > 0:
                beta_abs_changes = beta_changes.abs()
                beta_threshold = float(beta_clean.std()) * 0.1

                # 计算变化频率
                significant_changes = 0
                try:
                    for val in beta_abs_changes:
                        if float(val) > beta_threshold:
                            significant_changes += 1
                except:
                    significant_changes = 0

                beta_change_stats = {
                    'mean_change': float(beta_changes.mean()) if hasattr(beta_changes.mean(), '__float__') else 0.0,
                    'std_change': float(beta_changes.std()) if hasattr(beta_changes.std(), '__float__') else 0.0,
                    'max_abs_change': float(beta_abs_changes.max()) if hasattr(beta_abs_changes.max(), '__float__') else 0.0,
                    'change_frequency': float(significant_changes / len(beta_abs_changes)) if len(beta_abs_changes) > 0 else 0.0
                }
            else:
                beta_change_stats = {
                    'mean_change': 0.0,
                    'std_change': 0.0,
                    'max_abs_change': 0.0,
                    'change_frequency': 0.0
                }

            # 标准误差变化率
            std_err_changes = std_err_clean.diff().dropna()
            if len(std_err_changes) > 0:
                std_err_abs_changes = std_err_changes.abs()
                std_err_threshold = float(std_err_clean.std()) * 0.1

                std_err_change_stats = {
                    'mean_change': float(std_err_changes.mean()),
                    'std_change': float(std_err_changes.std()),
                    'max_abs_change': float(std_err_abs_changes.max()),
                    'change_frequency': float(sum(std_err_abs_changes > std_err_threshold) / len(std_err_abs_changes))
                }
            else:
                std_err_change_stats = {
                    'mean_change': 0.0,
                    'std_change': 0.0,
                    'max_abs_change': 0.0,
                    'change_frequency': 0.0
                }

            results['parameter_changes'] = {
                'beta_changes': beta_change_stats,
                'std_error_changes': std_err_change_stats
            }

            print(f"   Beta显著变化频率: {beta_change_stats['change_frequency']:.2%}")
            print(f"   标准误差显著变化频率: {std_err_change_stats['change_frequency']:.2%}")

        except Exception as e:
            print(f"   参数变化率分析失败: {str(e)}")
            results['parameter_changes'] = {'error': str(e)}

        self.results['parameter_analysis'] = results
        return results

    def generate_optimization_suggestions(self) -> Dict:
        """
        基于统计分析结果生成参数优化建议

        Returns:
            优化建议字典
        """
        print("\n=== 生成优化建议 ===")
        suggestions = {}

        # 1. 参数更新频率建议
        if 'parameter_analysis' in self.results:
            param_analysis = self.results['parameter_analysis']

            # 基于Beta稳定性建议更新频率
            if 'beta_analysis' in param_analysis:
                beta_stats = param_analysis['beta_analysis'].get('statistics', {})
                beta_cv = beta_stats.get('coefficient_of_variation', 0)

                if beta_cv < 0.05:  # 变异系数小于5%
                    update_freq_suggestion = "每5-10分钟"
                    reasoning = "Beta系数相对稳定，可以降低更新频率"
                elif beta_cv < 0.1:  # 变异系数小于10%
                    update_freq_suggestion = "每2-5分钟"
                    reasoning = "Beta系数中等稳定性"
                else:
                    update_freq_suggestion = "每1-2分钟"
                    reasoning = "Beta系数变化较大，需要频繁更新"

                suggestions['parameter_update_frequency'] = {
                    'current': "每1分钟",
                    'suggested': update_freq_suggestion,
                    'reasoning': reasoning,
                    'beta_coefficient_of_variation': beta_cv
                }

                print(f"参数更新频率建议: {update_freq_suggestion}")
                print(f"理由: {reasoning}")

        # 2. 窗口大小建议
        if 'spread_analysis' in self.results:
            spread_analysis = self.results['spread_analysis']

            # 基于自相关分析建议窗口大小
            if 'autocorrelation_analysis' in spread_analysis:
                autocorr = spread_analysis['autocorrelation_analysis']
                significant_lags = autocorr.get('significant_autocorr_lags', [])

                if len(significant_lags) > 0:
                    max_significant_lag = max(significant_lags[:10])  # 取前10个显著滞后期的最大值
                    suggested_window = max(40, max_significant_lag * 2)  # 至少是最大显著滞后期的2倍
                else:
                    suggested_window = 40  # 默认值

                suggestions['window_size'] = {
                    'current': 40,
                    'suggested': suggested_window,
                    'reasoning': f"基于自相关分析，最大显著滞后期为{max(significant_lags[:5]) if significant_lags else 0}",
                    'significant_lags_count': len(significant_lags)
                }

                print(f"窗口大小建议: {suggested_window}")

        # 3. 交易阈值建议
        if 'spread_analysis' in self.results:
            spread_analysis = self.results['spread_analysis']

            if 'distribution_analysis' in spread_analysis:
                dist_analysis = spread_analysis['distribution_analysis']
                desc_stats = dist_analysis.get('descriptive_stats', {})

                # 基于分布特征调整阈值
                skewness = desc_stats.get('skewness', 0)
                kurtosis = desc_stats.get('kurtosis', 0)

                if abs(skewness) > 1 or kurtosis > 3:  # 非正态分布
                    threshold_suggestion = "0.3-0.7"
                    reasoning = "价差分布偏离正态，建议使用较小的阈值倍数"
                else:
                    threshold_suggestion = "0.5-1.0"
                    reasoning = "价差分布接近正态，可使用标准阈值倍数"

                suggestions['threshold_multiplier'] = {
                    'current': 0.5,
                    'suggested': threshold_suggestion,
                    'reasoning': reasoning,
                    'skewness': skewness,
                    'kurtosis': kurtosis
                }

                print(f"阈值倍数建议: {threshold_suggestion}")
                print(f"理由: {reasoning}")

        self.results['optimization_suggestions'] = suggestions
        return suggestions

    def plot_analysis_results(self):
        """
        生成分析结果的可视化图表
        """
        print("\n=== 生成可视化图表 ===")

        if self.spread_series is None:
            print("警告: 没有价差序列数据，跳过可视化")
            return

        # 创建图表
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('ETF配对交易策略统计分析结果', fontsize=16, fontweight='bold')

        # 1. 价差序列时间序列图
        axes[0, 0].plot(self.spread_series.index, self.spread_series.values, alpha=0.7, linewidth=0.8)
        axes[0, 0].set_title('价差序列时间序列')
        axes[0, 0].set_xlabel('时间')
        axes[0, 0].set_ylabel('价差')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 价差序列分布直方图
        axes[0, 1].hist(self.spread_series.dropna(), bins=50, alpha=0.7, density=True, color='skyblue')
        axes[0, 1].set_title('价差序列分布')
        axes[0, 1].set_xlabel('价差值')
        axes[0, 1].set_ylabel('密度')
        axes[0, 1].grid(True, alpha=0.3)

        # 添加正态分布对比
        if 'spread_analysis' in self.results and 'distribution_analysis' in self.results['spread_analysis']:
            desc_stats = self.results['spread_analysis']['distribution_analysis'].get('descriptive_stats', {})
            if 'mean' in desc_stats and 'std' in desc_stats:
                x_norm = np.linspace(self.spread_series.min(), self.spread_series.max(), 100)
                y_norm = stats.norm.pdf(x_norm, desc_stats['mean'], desc_stats['std'])
                axes[0, 1].plot(x_norm, y_norm, 'r--', label='正态分布', linewidth=2)
                axes[0, 1].legend()

        # 3. Q-Q图
        try:
            from scipy.stats import probplot
            probplot(self.spread_series.dropna(), dist="norm", plot=axes[0, 2])
            axes[0, 2].set_title('Q-Q图 (正态性检验)')
            axes[0, 2].grid(True, alpha=0.3)
        except Exception as e:
            axes[0, 2].text(0.5, 0.5, f'Q-Q图生成失败:\n{str(e)}',
                           transform=axes[0, 2].transAxes, ha='center', va='center')
            axes[0, 2].set_title('Q-Q图 (生成失败)')

        # 4. Beta系数时间序列
        if self.beta_series is not None:
            axes[1, 0].plot(self.beta_series.index, self.beta_series.values, alpha=0.7, color='orange')
            axes[1, 0].set_title('Beta系数时间序列')
            axes[1, 0].set_xlabel('时间')
            axes[1, 0].set_ylabel('Beta值')
            axes[1, 0].grid(True, alpha=0.3)
        else:
            axes[1, 0].text(0.5, 0.5, '无Beta系数数据', transform=axes[1, 0].transAxes, ha='center', va='center')
            axes[1, 0].set_title('Beta系数时间序列')

        # 5. 标准误差时间序列
        if self.std_error_series is not None:
            axes[1, 1].plot(self.std_error_series.index, self.std_error_series.values, alpha=0.7, color='green')
            axes[1, 1].set_title('标准误差时间序列')
            axes[1, 1].set_xlabel('时间')
            axes[1, 1].set_ylabel('标准误差')
            axes[1, 1].grid(True, alpha=0.3)
        else:
            axes[1, 1].text(0.5, 0.5, '无标准误差数据', transform=axes[1, 1].transAxes, ha='center', va='center')
            axes[1, 1].set_title('标准误差时间序列')

        # 6. 自相关函数图
        if ('spread_analysis' in self.results and
            'autocorrelation_analysis' in self.results['spread_analysis'] and
            'autocorrelation' in self.results['spread_analysis']['autocorrelation_analysis']):

            autocorr_data = self.results['spread_analysis']['autocorrelation_analysis']['autocorrelation']
            if 'acf_values' in autocorr_data and autocorr_data['acf_values']:
                acf_values = autocorr_data['acf_values']
                lags = range(len(acf_values))
                axes[1, 2].bar(lags, acf_values, alpha=0.7, color='purple')
                axes[1, 2].axhline(y=0, color='black', linestyle='-', alpha=0.3)
                axes[1, 2].axhline(y=0.05, color='red', linestyle='--', alpha=0.5, label='5%显著性水平')
                axes[1, 2].axhline(y=-0.05, color='red', linestyle='--', alpha=0.5)
                axes[1, 2].set_title('自相关函数')
                axes[1, 2].set_xlabel('滞后期')
                axes[1, 2].set_ylabel('自相关系数')
                axes[1, 2].legend()
                axes[1, 2].grid(True, alpha=0.3)
        else:
            axes[1, 2].text(0.5, 0.5, '无自相关数据', transform=axes[1, 2].transAxes, ha='center', va='center')
            axes[1, 2].set_title('自相关函数')

        plt.tight_layout()

        # 保存图表
        plot_path = os.path.join(self.output_dir, 'statistical_analysis_plots.png')
        plt.savefig(plot_path, dpi=300, bbox_inches='tight')
        print(f"可视化图表已保存至: {plot_path}")

        # plt.show()  # 禁用显示以避免在无头环境中出现问题

        return plot_path

    def generate_comprehensive_report(self) -> str:
        """
        生成综合统计分析报告

        Returns:
            报告文件路径
        """
        print("\n=== 生成综合分析报告 ===")

        report_lines = []
        report_lines.append("# ETF配对交易策略统计分析报告")
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("=" * 80)
        report_lines.append("")

        # 1. 数据概览
        report_lines.append("## 1. 数据概览")
        if self.price_data is not None:
            report_lines.append(f"- 价格数据: {len(self.price_data)} 个数据点")
            report_lines.append(f"- ETF对: {', '.join(self.price_data.columns)}")
            report_lines.append(f"- 时间范围: {self.price_data.index[0]} 至 {self.price_data.index[-1]}")

        if self.spread_series is not None:
            report_lines.append(f"- 价差序列: {len(self.spread_series)} 个有效数据点")
        report_lines.append("")

        # 2. 价差序列统计特性分析
        report_lines.append("## 2. 价差序列统计特性分析")

        if 'spread_analysis' in self.results:
            spread_analysis = self.results['spread_analysis']

            # 2.1 白噪声检验结果
            report_lines.append("### 2.1 白噪声检验")
            if 'white_noise_tests' in spread_analysis:
                white_noise = spread_analysis['white_noise_tests']

                # Ljung-Box检验
                if 'ljung_box' in white_noise and 'error' not in white_noise['ljung_box']:
                    report_lines.append("**Ljung-Box检验 (序列相关性):**")
                    for lag_key, result in white_noise['ljung_box'].items():
                        lag = lag_key.split('_')[1]
                        report_lines.append(f"- 滞后{lag}期: 统计量={result['statistic']:.4f}, "
                                          f"p值={result['p_value']:.4f}, "
                                          f"白噪声={'是' if result['is_white_noise'] else '否'}")

                # ADF检验
                if 'adf' in white_noise and 'error' not in white_noise['adf']:
                    adf = white_noise['adf']
                    report_lines.append("**ADF平稳性检验:**")
                    report_lines.append(f"- 统计量: {adf['statistic']:.4f}")
                    report_lines.append(f"- p值: {adf['p_value']:.4f}")
                    report_lines.append(f"- 平稳性: {'是' if adf['is_stationary'] else '否'}")

                # KPSS检验
                if 'kpss' in white_noise and 'error' not in white_noise['kpss']:
                    kpss = white_noise['kpss']
                    report_lines.append("**KPSS平稳性检验:**")
                    report_lines.append(f"- 统计量: {kpss['statistic']:.4f}")
                    report_lines.append(f"- p值: {kpss['p_value']:.4f}")
                    report_lines.append(f"- 平稳性: {'是' if kpss['is_stationary'] else '否'}")

            report_lines.append("")

            # 2.2 分布特征分析
            report_lines.append("### 2.2 分布特征分析")
            if 'distribution_analysis' in spread_analysis:
                dist_analysis = spread_analysis['distribution_analysis']

                if 'descriptive_stats' in dist_analysis:
                    stats_data = dist_analysis['descriptive_stats']
                    report_lines.append("**描述性统计:**")
                    report_lines.append(f"- 样本数量: {stats_data['count']}")
                    report_lines.append(f"- 均值: {stats_data['mean']:.6f}")
                    report_lines.append(f"- 标准差: {stats_data['std']:.6f}")
                    report_lines.append(f"- 偏度: {stats_data['skewness']:.4f}")
                    report_lines.append(f"- 峰度: {stats_data['kurtosis']:.4f}")
                    report_lines.append(f"- 最小值: {stats_data['min']:.6f}")
                    report_lines.append(f"- 最大值: {stats_data['max']:.6f}")

                if 'normality_tests' in dist_analysis:
                    norm_tests = dist_analysis['normality_tests']
                    report_lines.append("**正态性检验:**")

                    for test_name, result in norm_tests.items():
                        if 'error' not in result:
                            test_display_name = {
                                'shapiro_wilk': 'Shapiro-Wilk',
                                'kolmogorov_smirnov': 'Kolmogorov-Smirnov',
                                'jarque_bera': 'Jarque-Bera'
                            }.get(test_name, test_name)

                            report_lines.append(f"- {test_display_name}: 统计量={result['statistic']:.4f}, "
                                              f"p值={result['p_value']:.4f}, "
                                              f"正态分布={'是' if result['is_normal'] else '否'}")

            report_lines.append("")

            # 2.3 自相关性分析
            report_lines.append("### 2.3 自相关性和聚集性分析")
            if 'autocorrelation_analysis' in spread_analysis:
                autocorr = spread_analysis['autocorrelation_analysis']

                if 'significant_autocorr_lags' in autocorr:
                    sig_lags = autocorr['significant_autocorr_lags']
                    report_lines.append(f"**显著自相关滞后期:** {sig_lags[:10] if len(sig_lags) > 10 else sig_lags}")

                if 'arch_test' in autocorr and 'error' not in autocorr['arch_test']:
                    arch = autocorr['arch_test']
                    report_lines.append("**ARCH效应检验:**")
                    report_lines.append(f"- LM统计量: {arch['lm_statistic']:.4f}")
                    report_lines.append(f"- p值: {arch['p_value']:.4f}")
                    report_lines.append(f"- ARCH效应: {'存在' if arch['has_arch_effect'] else '不存在'}")

                if 'periodicity' in autocorr and 'error' not in autocorr['periodicity']:
                    period = autocorr['periodicity']
                    if 'dominant_periods' in period and period['dominant_periods']:
                        periods = [f"{p:.1f}" for p in period['dominant_periods'][:3]]
                        report_lines.append(f"**主要周期:** {', '.join(periods)} 个数据点")

        report_lines.append("")

        # 3. 回归参数时变性分析
        report_lines.append("## 3. 回归参数时变性分析")

        if 'parameter_analysis' in self.results:
            param_analysis = self.results['parameter_analysis']

            # 3.1 Beta系数分析
            if 'beta_analysis' in param_analysis:
                beta_analysis = param_analysis['beta_analysis']

                report_lines.append("### 3.1 Beta系数分析")
                if 'statistics' in beta_analysis:
                    beta_stats = beta_analysis['statistics']
                    report_lines.append("**Beta系数统计特征:**")
                    report_lines.append(f"- 均值: {beta_stats['mean']:.4f}")
                    report_lines.append(f"- 标准差: {beta_stats['std']:.4f}")
                    report_lines.append(f"- 变异系数: {beta_stats['coefficient_of_variation']:.4f}")
                    report_lines.append(f"- 范围比: {beta_stats['range_ratio']:.4f}")

                if 'stability' in beta_analysis and 'error' not in beta_analysis['stability']:
                    beta_stability = beta_analysis['stability']
                    report_lines.append("**Beta系数平稳性:**")
                    report_lines.append(f"- ADF统计量: {beta_stability['adf_statistic']:.4f}")
                    report_lines.append(f"- p值: {beta_stability['adf_p_value']:.4f}")
                    report_lines.append(f"- 平稳性: {'是' if beta_stability['is_stationary'] else '否'}")

            # 3.2 标准误差分析
            if 'std_error_analysis' in param_analysis:
                std_err_analysis = param_analysis['std_error_analysis']

                report_lines.append("### 3.2 标准误差分析")
                if 'statistics' in std_err_analysis:
                    std_err_stats = std_err_analysis['statistics']
                    report_lines.append("**标准误差统计特征:**")
                    report_lines.append(f"- 均值: {std_err_stats['mean']:.6f}")
                    report_lines.append(f"- 标准差: {std_err_stats['std']:.6f}")
                    report_lines.append(f"- 变异系数: {std_err_stats['coefficient_of_variation']:.4f}")

                if 'stability' in std_err_analysis and 'error' not in std_err_analysis['stability']:
                    std_err_stability = std_err_analysis['stability']
                    report_lines.append("**标准误差平稳性:**")
                    report_lines.append(f"- ADF统计量: {std_err_stability['adf_statistic']:.4f}")
                    report_lines.append(f"- p值: {std_err_stability['adf_p_value']:.4f}")
                    report_lines.append(f"- 平稳性: {'是' if std_err_stability['is_stationary'] else '否'}")

            # 3.3 参数变化率分析
            if 'parameter_changes' in param_analysis:
                changes = param_analysis['parameter_changes']

                report_lines.append("### 3.3 参数变化率分析")
                if 'beta_changes' in changes and 'error' not in changes['beta_changes']:
                    beta_changes = changes['beta_changes']
                    report_lines.append("**Beta系数变化特征:**")
                    report_lines.append(f"- 平均变化: {beta_changes['mean_change']:.6f}")
                    report_lines.append(f"- 变化标准差: {beta_changes['std_change']:.6f}")
                    report_lines.append(f"- 最大绝对变化: {beta_changes['max_abs_change']:.6f}")
                    report_lines.append(f"- 显著变化频率: {beta_changes['change_frequency']:.2%}")

                if 'std_error_changes' in changes and 'error' not in changes['std_error_changes']:
                    std_err_changes = changes['std_error_changes']
                    report_lines.append("**标准误差变化特征:**")
                    report_lines.append(f"- 平均变化: {std_err_changes['mean_change']:.8f}")
                    report_lines.append(f"- 变化标准差: {std_err_changes['std_change']:.8f}")
                    report_lines.append(f"- 最大绝对变化: {std_err_changes['max_abs_change']:.8f}")
                    report_lines.append(f"- 显著变化频率: {std_err_changes['change_frequency']:.2%}")

        report_lines.append("")

        # 4. 优化建议
        report_lines.append("## 4. 策略优化建议")

        if 'optimization_suggestions' in self.results:
            suggestions = self.results['optimization_suggestions']

            if 'parameter_update_frequency' in suggestions:
                freq_suggestion = suggestions['parameter_update_frequency']
                report_lines.append("### 4.1 参数更新频率建议")
                report_lines.append(f"- 当前频率: {freq_suggestion['current']}")
                report_lines.append(f"- 建议频率: {freq_suggestion['suggested']}")
                report_lines.append(f"- 理由: {freq_suggestion['reasoning']}")
                report_lines.append(f"- Beta变异系数: {freq_suggestion['beta_coefficient_of_variation']:.4f}")

            if 'window_size' in suggestions:
                window_suggestion = suggestions['window_size']
                report_lines.append("### 4.2 窗口大小建议")
                report_lines.append(f"- 当前窗口: {window_suggestion['current']}")
                report_lines.append(f"- 建议窗口: {window_suggestion['suggested']}")
                report_lines.append(f"- 理由: {window_suggestion['reasoning']}")

            if 'threshold_multiplier' in suggestions:
                threshold_suggestion = suggestions['threshold_multiplier']
                report_lines.append("### 4.3 交易阈值建议")
                report_lines.append(f"- 当前阈值倍数: {threshold_suggestion['current']}")
                report_lines.append(f"- 建议阈值倍数: {threshold_suggestion['suggested']}")
                report_lines.append(f"- 理由: {threshold_suggestion['reasoning']}")
                report_lines.append(f"- 价差偏度: {threshold_suggestion['skewness']:.4f}")
                report_lines.append(f"- 价差峰度: {threshold_suggestion['kurtosis']:.4f}")

        report_lines.append("")
        report_lines.append("## 5. 总结")
        report_lines.append("基于以上统计分析结果，建议:")
        report_lines.append("1. 根据参数稳定性调整更新频率，以降低计算成本")
        report_lines.append("2. 根据自相关分析结果优化回归窗口大小")
        report_lines.append("3. 根据价差分布特征调整交易阈值")
        report_lines.append("4. 持续监控参数稳定性，必要时重新评估策略参数")

        # 保存报告
        report_content = '\n'.join(report_lines)
        report_path = os.path.join(self.output_dir, 'statistical_analysis_report.md')

        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        print(f"综合分析报告已保存至: {report_path}")
        return report_path

    def run_complete_analysis(self, price_data: pd.DataFrame, window: int = 40, std_dev_mult: float = 0.5) -> Dict:
        """
        运行完整的统计分析流程

        Args:
            price_data: 价格数据
            window: rolling regression窗口大小
            std_dev_mult: 标准差倍数

        Returns:
            完整分析结果
        """
        print("开始ETF配对交易策略统计分析...")
        print("=" * 60)

        # 1. 加载数据
        self.load_data(price_data)

        # 2. 计算价差序列
        self.calculate_spread_series(window=window, std_dev_mult=std_dev_mult)

        # 3. 执行各项统计分析
        self.white_noise_tests()
        self.distribution_analysis()
        self.autocorrelation_analysis()
        self.parameter_stability_analysis()

        # 4. 生成优化建议
        self.generate_optimization_suggestions()

        # 5. 生成可视化图表
        self.plot_analysis_results()

        # 6. 生成综合报告
        self.generate_comprehensive_report()

        print("\n" + "=" * 60)
        print("统计分析完成！")
        print(f"结果保存在目录: {self.output_dir}")

        return self.results
