#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.strategy.pairs_strategy import PairsStrategy
from src.engine.engine import BacktestEngine

def load_csv_data(file_path):
    """加载CSV文件数据"""
    try:
        return pd.read_csv(file_path, index_col=0, parse_dates=True)
    except Exception as e:
        print(f"加载数据文件失败: {e}")
        return None

def analyze_trades(trades_file, signals_file=None, specific_time=None):
    """分析交易记录和信号"""
    # 加载交易记录
    trades_df = pd.read_csv(trades_file)
    trades_df['datetime'] = pd.to_datetime(trades_df['datetime'])
    
    # 基本信息
    print(f"\n===== 交易统计 =====")
    print(f"总交易次数: {len(trades_df)}")
    print(f"首次交易时间: {trades_df['datetime'].min()}")
    print(f"最后交易时间: {trades_df['datetime'].max()}")
    
    # 计算交易统计
    buy_trades = trades_df[trades_df['direction'] > 0]
    sell_trades = trades_df[trades_df['direction'] < 0]
    
    print(f"\n买入交易: {len(buy_trades)} 次")
    print(f"卖出交易: {len(sell_trades)} 次")
    
    # 按交易对象统计
    symbols = trades_df['symbol'].unique()
    for symbol in symbols:
        symbol_trades = trades_df[trades_df['symbol'] == symbol]
        symbol_buys = symbol_trades[symbol_trades['direction'] > 0]
        symbol_sells = symbol_trades[symbol_trades['direction'] < 0]
        
        print(f"\n{symbol} 交易:")
        print(f"  买入次数: {len(symbol_buys)} 次")
        print(f"  卖出次数: {len(symbol_sells)} 次")
        
        if len(symbol_buys) > 0:
            avg_buy_price = symbol_buys['price'].mean()
            print(f"  平均买入价格: {avg_buy_price:.4f}")
        
        if len(symbol_sells) > 0:
            avg_sell_price = symbol_sells['price'].mean()
            print(f"  平均卖出价格: {avg_sell_price:.4f}")
    
    # 如果提供了信号文件，加载并分析
    if signals_file and os.path.exists(signals_file):
        signals_df = load_csv_data(signals_file)
        
        if signals_df is not None:
            # 输出信号数据的基本统计信息
            print(f"\n===== 信号统计 =====")
            signal_counts = signals_df['trade_signal'].value_counts()
            print(f"信号分布:")
            print(f"  无信号 (0): {signal_counts.get(0, 0)} 次")
            print(f"  买入信号 (1): {signal_counts.get(1, 0)} 次")
            print(f"  卖出信号 (-1): {signal_counts.get(-1, 0)} 次")
            
            # 如果指定了特定时间点，分析该时间点的信号
            if specific_time:
                try:
                    timestamp = pd.Timestamp(specific_time)
                    if timestamp not in signals_df.index:
                        closest_idx = signals_df.index.get_indexer([timestamp], method='nearest')[0]
                        timestamp = signals_df.index[closest_idx]
                        print(f"\n找到最接近的时间点: {timestamp}")
                    
                    signal_data = signals_df.loc[timestamp]
                    print(f"\n===== {timestamp} 信号分析 =====")
                    print(f"价格比率: {signal_data['price_ratio']:.6f}")
                    print(f"移动平均(Beta): {signal_data['ma']:.6f}")
                    print(f"交易上界: {signal_data['upper_bound']:.6f}")
                    print(f"交易下界: {signal_data['lower_bound']:.6f}")
                    print(f"标准差: {signal_data['std']:.6f}")
                    
                    # 输出信号判断
                    trade_signal = signal_data['trade_signal']
                    signal_text = '无信号'
                    if trade_signal == 1:
                        signal_text = f"买入信号 (1) - 价格比率低于下界"
                    elif trade_signal == -1:
                        signal_text = f"卖出信号 (-1) - 价格比率高于上界"
                    
                    print(f"交易信号: {signal_text}")
                    
                    # 查找该时间点附近的交易
                    time_diff = pd.Timedelta(minutes=5)
                    nearby_trades = trades_df[
                        (trades_df['datetime'] >= timestamp - time_diff) & 
                        (trades_df['datetime'] <= timestamp + time_diff)
                    ]
                    
                    if len(nearby_trades) > 0:
                        print(f"\n该时间点附近5分钟内的交易:")
                        for _, trade in nearby_trades.iterrows():
                            trade_type = "买入" if trade['direction'] > 0 else "卖出"
                            print(f"  {trade['datetime']}: {trade_type} {trade['symbol']} {trade['volume']}股，价格: {trade['price']:.4f}")
                    else:
                        print("\n该时间点附近5分钟内无交易")
                except Exception as e:
                    print(f"分析特定时间点失败: {e}")
    
    # 绘制权益曲线
    if 'equity_after_trade' in trades_df.columns:
        plt.figure(figsize=(12, 6))
        plt.plot(trades_df['datetime'], trades_df['equity_after_trade'])
        plt.title('交易后权益曲线')
        plt.xlabel('时间')
        plt.ylabel('权益')
        plt.grid(True)
        plt.tight_layout()
        
        # 保存图表
        output_dir = os.path.dirname(trades_file)
        plt.savefig(os.path.join(output_dir, 'equity_curve.png'))
        plt.close()
        print(f"\n权益曲线图已保存至: {os.path.join(output_dir, 'equity_curve.png')}")

def main():
    parser = argparse.ArgumentParser(description='分析配对交易的交易记录和信号')
    parser.add_argument('--trades', type=str, required=True, help='交易记录CSV文件路径')
    parser.add_argument('--signals', type=str, help='信号数据CSV文件路径')
    parser.add_argument('--time', type=str, help='分析特定时间点的信号 (格式: YYYY-MM-DD HH:MM:SS)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.trades):
        print(f"错误: 交易记录文件不存在: {args.trades}")
        return
    
    analyze_trades(args.trades, args.signals, args.time)

if __name__ == '__main__':
    main() 