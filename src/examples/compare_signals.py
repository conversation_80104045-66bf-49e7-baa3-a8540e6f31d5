#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import sys
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

def load_csv_data(file_path):
    """加载CSV文件数据"""
    try:
        return pd.read_csv(file_path, index_col=0, parse_dates=True)
    except Exception as e:
        print(f"加载数据文件失败: {e}")
        return None

def compare_signals(signals_file1, signals_file2, specific_time):
    """比较两个不同时间窗口回测的同一时间点的信号数据"""
    # 加载信号数据
    signals_df1 = load_csv_data(signals_file1)
    signals_df2 = load_csv_data(signals_file2)
    
    if signals_df1 is None or signals_df2 is None:
        print("无法加载信号数据文件")
        return
    
    # 解析特定时间点
    timestamp = pd.Timestamp(specific_time)
    
    # 在两个数据集中找到最接近的时间点
    if timestamp not in signals_df1.index:
        closest_idx1 = signals_df1.index.get_indexer([timestamp], method='nearest')[0]
        time1 = signals_df1.index[closest_idx1]
        print(f"数据集1中找到最接近的时间点: {time1}")
    else:
        time1 = timestamp
    
    if timestamp not in signals_df2.index:
        closest_idx2 = signals_df2.index.get_indexer([timestamp], method='nearest')[0]
        time2 = signals_df2.index[closest_idx2]
        print(f"数据集2中找到最接近的时间点: {time2}")
    else:
        time2 = timestamp
    
    # 获取信号数据
    signal_data1 = signals_df1.loc[time1]
    signal_data2 = signals_df2.loc[time2]
    
    # 提取文件名作为标识
    file_name1 = os.path.basename(signals_file1).split('_')[2:4]
    file_name2 = os.path.basename(signals_file2).split('_')[2:4]
    
    label1 = f"{file_name1[0]}-{file_name1[1].split('.')[0]}"
    label2 = f"{file_name2[0]}-{file_name2[1].split('.')[0]}"
    
    # 打印比较结果
    print(f"\n===== 信号对比分析: {timestamp} =====")
    print(f"{'参数':<15} | {label1:<20} | {label2:<20} | 差值")
    print("-" * 70)
    
    # 比较主要指标
    compare_fields = [
        ('price_ratio', "价格比率", 6),
        ('ma', "移动平均(Beta)", 6),
        ('upper_bound', "交易上界", 6),
        ('lower_bound', "交易下界", 6),
        ('std', "标准差", 6),
    ]
    
    for field, label, precision in compare_fields:
        val1 = signal_data1[field]
        val2 = signal_data2[field]
        diff = val1 - val2
        diff_pct = diff / val1 * 100 if val1 != 0 else 0
        
        print(f"{label:<15} | {val1:<20.{precision}f} | {val2:<20.{precision}f} | {diff:.{precision}f} ({diff_pct:.4f}%)")
    
    # 比较交易信号
    signal1 = signal_data1['trade_signal']
    signal2 = signal_data2['trade_signal']
    
    signal_text1 = '无信号'
    if signal1 == 1:
        signal_text1 = "买入信号 (1)"
    elif signal1 == -1:
        signal_text1 = "卖出信号 (-1)"
    
    signal_text2 = '无信号'
    if signal2 == 1:
        signal_text2 = "买入信号 (1)"
    elif signal2 == -1:
        signal_text2 = "卖出信号 (-1)"
    
    print(f"交易信号      | {signal_text1:<20} | {signal_text2:<20} | {'相同' if signal1 == signal2 else '不同'}")
    
    # 绘制价格比率与交易区间的对比图
    plt.figure(figsize=(14, 7))
    
    # 提取两个数据集中靠近目标时间点的数据（前后各30个点）
    idx1 = signals_df1.index.get_loc(time1)
    idx2 = signals_df2.index.get_loc(time2)
    
    window_size = 30
    start_idx1 = max(0, idx1 - window_size)
    end_idx1 = min(len(signals_df1), idx1 + window_size)
    
    start_idx2 = max(0, idx2 - window_size)
    end_idx2 = min(len(signals_df2), idx2 + window_size)
    
    subset1 = signals_df1.iloc[start_idx1:end_idx1]
    subset2 = signals_df2.iloc[start_idx2:end_idx2]
    
    # 绘制第一个数据集
    plt.subplot(2, 1, 1)
    plt.plot(subset1.index, subset1['price_ratio'], 'b-', label='价格比率')
    plt.plot(subset1.index, subset1['upper_bound'], 'r--', label='上界')
    plt.plot(subset1.index, subset1['lower_bound'], 'g--', label='下界')
    plt.axvline(x=time1, color='k', linestyle='-', alpha=0.3, label='分析时间点')
    plt.title(f'数据集1 ({label1}) 价格比率与交易区间')
    plt.legend()
    plt.grid(True)
    
    # 绘制第二个数据集
    plt.subplot(2, 1, 2)
    plt.plot(subset2.index, subset2['price_ratio'], 'b-', label='价格比率')
    plt.plot(subset2.index, subset2['upper_bound'], 'r--', label='上界')
    plt.plot(subset2.index, subset2['lower_bound'], 'g--', label='下界')
    plt.axvline(x=time2, color='k', linestyle='-', alpha=0.3, label='分析时间点')
    plt.title(f'数据集2 ({label2}) 价格比率与交易区间')
    plt.legend()
    plt.grid(True)
    
    plt.tight_layout()
    
    # 保存图表
    output_dir = os.path.dirname(signals_file1)
    plt.savefig(os.path.join(output_dir, f'signals_comparison_{timestamp.strftime("%Y%m%d_%H%M%S")}.png'))
    plt.close()
    print(f"\n比较图已保存至: {os.path.join(output_dir, f'signals_comparison_{timestamp.strftime('%Y%m%d_%H%M%S')}.png')}")

def main():
    parser = argparse.ArgumentParser(description='比较不同时间窗口回测的同一时间点的信号数据')
    parser.add_argument('--signals1', type=str, required=True, help='第一个信号数据CSV文件路径')
    parser.add_argument('--signals2', type=str, required=True, help='第二个信号数据CSV文件路径')
    parser.add_argument('--time', type=str, required=True, help='分析特定时间点的信号 (格式: YYYY-MM-DD HH:MM:SS)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.signals1):
        print(f"错误: 信号文件不存在: {args.signals1}")
        return
    
    if not os.path.exists(args.signals2):
        print(f"错误: 信号文件不存在: {args.signals2}")
        return
    
    compare_signals(args.signals1, args.signals2, args.time)

if __name__ == '__main__':
    main() 