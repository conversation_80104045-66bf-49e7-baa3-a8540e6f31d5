import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union
from statsmodels.regression.linear_model import OLS
from statsmodels.regression.rolling import RollingOLS
import datetime
import os

class PairsStrategy:
    """ETF配对交易策略 - 使用基于滚动回归和beta标准误差计算交易区间"""
    
    def __init__(
        self,
        window: int = 10,
        std_dev_mult: float = 1.2,
        max_pos_size: float = 1.0,
        verbose: bool = True,
        output_dir: str = "results",
    ):
        """
        初始化策略参数
        Args:
            window: 回归窗口大小（使用过去多少个交易日的数据点）
            std_dev_mult: beta标准误差的乘数，用于计算交易区间
            max_pos_size: 最大持仓比例
            verbose: 是否输出交易日志
            output_dir: 输出目录，用于保存订单记录
        """
        self.window = window
        self.std_dev_mult = std_dev_mult
        self.max_pos_size = max_pos_size
        self.verbose = verbose
        self.output_dir = output_dir
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 运行时数据
        self.engine = None
        self.pairs = []
        self.current_position = None  # 记录当前持仓的ETF
        
        # 存储历史数据和信号
        self.price_history = None
        self.signals = None
        self.commission_rate = None  # 将在initialize时从engine获取
        self.slippage_rate = None
        
        # 存储生成的订单
        self.all_orders = []
    
    def initialize(self, engine) -> None:
        """策略初始化"""
        self.engine = engine
        self.pairs = list(engine.data.columns)
        if len(self.pairs) != 2:
            raise ValueError("策略需要两个ETF的数据")
        
        # 获取手续费率
        self.commission_rate = engine.commission_rate
        self.slippage_rate = engine.slippage_rate
        
        # 初始化历史数据DataFrame
        self.price_history = engine.data.copy()
        self.calculate_signals()
    
    def calculate_signals(self) -> None:
        """
        计算所有交易信号
        - 使用RollingOLS实现滚动回归
        - 基于回归的beta和beta标准误差计算交易区间
        - 考虑交易手续费和滑点的影响
        - 向量化计算以提高效率
        """
        etf1, etf2 = self.pairs
        
        # 计算价格比率
        price_ratio = self.price_history[etf1] / self.price_history[etf2]
        
        # 准备回归数据
        y = self.price_history[etf1]
        X = self.price_history[etf2]
        
        # 使用RollingOLS执行滚动回归
        try:
            # 设置X矩阵，不包含常数项
            
            # 执行滚动回归，指定窗口大小
            rolling_model = RollingOLS(y, X, window=self.window)
            rolling_results = rolling_model.fit()
            
            # 获取滚动回归的beta和标准误差
            beta_values = rolling_results.params[etf2].shift(1)
            beta_std_values = rolling_results.bse[etf2].shift(1)
            
            # 计算交易区间
            upper_values = beta_values + self.std_dev_mult * beta_std_values
            lower_values = beta_values - self.std_dev_mult * beta_std_values
            
            # 计算考虑手续费和滑点的交易边界
            # 买入ETF1/卖出ETF2的边界 (下界): 需要价格比率更低才能覆盖成本
            trading_lower = lower_values / ((1 + self.commission_rate) * (1 + self.slippage_rate))**2
            # 卖出ETF1/买入ETF2的边界 (上界): 需要价格比率更高才能覆盖成本
            trading_upper = upper_values * ((1 + self.commission_rate) * (1 + self.slippage_rate))**2
            
            # 创建信号DataFrame
            self.signals = pd.DataFrame({
                'ma': beta_values,  # 使用滚动beta作为均值
                'std': beta_std_values,  # 使用滚动beta标准误差
                'upper_bound': trading_upper,  # 实际交易用的上边界（含手续费）
                'lower_bound': trading_lower,  # 实际交易用的下边界（含手续费）
                'raw_upper': upper_values,     # 原始上边界（不含手续费）
                'raw_lower': lower_values,     # 原始下边界（不含手续费）
                'etf2_price': self.price_history[etf2],
                'etf1_price': self.price_history[etf1],
                'price_ratio': price_ratio,
                'beta': beta_values  # 保存beta值
            }, index=self.price_history.index)
            
            # 预计算交易信号
            # 当价格比率 > 上限时：卖出ETF1, 买入ETF2 (卖高买低) -> 信号为-1
            # 当价格比率 < 下限时：买入ETF1, 卖出ETF2 (买低卖高) -> 信号为1
            self.signals['trade_signal'] = np.select(
                [
                    self.signals['price_ratio'] > self.signals['upper_bound'],
                    self.signals['price_ratio'] < self.signals['lower_bound']
                ],
                [-1, 1],  # -1表示卖ETF1买ETF2，1表示买ETF1卖ETF2
                default=0  # 默认不交易
            )
            
            # 创建索引位置映射，用于快速查找
            self.timestamp_to_idx = {ts: i for i, ts in enumerate(self.signals.index)}
            
        except Exception as e:
            if self.verbose:
                print(f"Rolling regression failed: {e}")
                import traceback
                traceback.print_exc()
    
    def calculate_position_size(self, price: float) -> int:
        """
        计算持仓规模
        Args:
            price: 交易价格
        Returns:
            交易数量
        """
        available_capital = self.engine.portfolio['cash'] * self.max_pos_size
        
        # 确保返回非负值
        size = max(0, int(available_capital / price))
        
        if self.verbose and available_capital <= 0:
            print(f"警告: 可用资金为{available_capital:.2f}，无法计算有效的持仓规模")
        
        return size
    
    def on_bar(self, timestamp: pd.Timestamp, row) -> List[Dict]:
        """
        处理每个时间点的数据
        Args:
            timestamp: 当前时间戳
            row: 当前数据行（namedtuple）
        Returns:
            订单列表
        """
        orders = []
        etf1, etf2 = self.pairs
        
        # 使用索引直接获取当前时间点的信号（避免使用.loc，速度更快）
        idx = self.timestamp_to_idx.get(timestamp)
        if idx is None:
            return orders  # 找不到对应的时间戳，不交易
        
        # 使用 .iat 和 .iloc 直接通过整数索引访问，比.loc更快
        ma = self.signals['ma'].iat[idx]
        std = self.signals['std'].iat[idx]
        
        # 如果数据不足，不进行交易
        if pd.isna(ma) or pd.isna(std):
            return orders
        
        # 获取预计算的交易信号
        trade_signal = self.signals['trade_signal'].iat[idx]
        
        # 根据交易信号决定交易目标
        if trade_signal == 0:
            return orders  # 无交易信号
        
        # 确定目标ETF
        target_etf = etf1 if trade_signal == 1 else etf2
            
        # 如果需要切换持仓
        if self.current_position != target_etf:
            # 直接从索引获取价格比率和边界，比使用.loc更快
            price_ratio = self.signals['price_ratio'].iat[idx]
            upper_bound = self.signals['upper_bound'].iat[idx]
            lower_bound = self.signals['lower_bound'].iat[idx]
            
            if self.verbose:
                print(f"{timestamp}: 切换持仓信号")
                print(f"价格比率: {price_ratio:.4f}, 上界: {upper_bound:.4f}, 下界: {lower_bound:.4f}")
                print(f"从 {self.current_position if self.current_position else '无持仓'} 切换到 {target_etf}")
            
            # 计算平仓后的预期可用资金
            expected_cash = self.engine.portfolio['cash']
            for symbol in [etf1, etf2]:
                position = self.engine.portfolio['positions'].get(symbol, 0)
                if position != 0:
                    # 估算平仓后会增加的资金
                    symbol_price = getattr(row, f"_{self.pairs.index(symbol)+1}") if hasattr(row, "_1") else row[symbol]
                    expected_cash += abs(position) * symbol_price
                    # 减去手续费
                    expected_cash -= abs(position) * symbol_price * self.commission_rate
            
            # 先清空所有持仓
            for symbol in [etf1, etf2]:
                position = self.engine.portfolio['positions'].get(symbol, 0)
                if position != 0:
                    # 针对namedtuple类型的row获取价格
                    price = getattr(row, f"_{self.pairs.index(symbol)+1}") if hasattr(row, "_1") else row[symbol]
                    order = {
                        'symbol': symbol,
                        'direction': -1 if position > 0 else 1,  # 平仓方向与持仓相反
                        'volume': abs(position),
                        'price': price,
                        'datetime': timestamp
                    }
                    orders.append(order)
                    
                    # 添加到全部订单列表
                    self.all_orders.append(order)
            
            # 针对namedtuple类型的row获取价格
            target_price = getattr(row, f"_{self.pairs.index(target_etf)+1}") if hasattr(row, "_1") else row[target_etf]
            
            # 使用预期可用资金计算新持仓数量
            # 考虑留出一部分资金作为手续费和滑点
            available_capital = expected_cash * self.max_pos_size
            size = int(available_capital / target_price)
            
            if self.verbose:
                print(f"目标ETF: {target_etf}, 价格: {target_price:.4f}")
                print(f"当前现金: {self.engine.portfolio['cash']:.2f}, 预期可用资金: {expected_cash:.2f}")
                print(f"计算出的持仓规模: {size}")
            
            # 建立新持仓（确保size为正数）
            if size > 0:
                order = {
                    'symbol': target_etf,
                    'direction': 1,  # 买入
                    'volume': size,
                    'price': target_price,
                    'datetime': timestamp
                }
                orders.append(order)
                
                # 添加到全部订单列表
                self.all_orders.append(order)
            else:
                if self.verbose:
                    print(f"警告: 计算出的持仓规模为{size}，小于等于0，跳过买入{target_etf}")
            
            self.current_position = target_etf
        
        return orders
    
    def save_orders_to_csv(self) -> None:
        """
        将策略生成的所有订单保存到CSV文件
        """
        if not self.all_orders:
            if self.verbose:
                print("没有订单记录可保存")
            return
        
        # 构建文件名
        start_date = self.price_history.index[0].strftime('%Y%m%d')
        end_date = self.price_history.index[-1].strftime('%Y%m%d')
        pairs_str = self.pairs
        
        filename = f"orders_{pairs_str}_{start_date}_{end_date}.csv"
        filepath = os.path.join(self.output_dir, filename)
        
        # 转换为DataFrame并保存
        orders_df = pd.DataFrame(self.all_orders)
        
        # 添加订单类型字段
        orders_df['type'] = orders_df.apply(
            lambda x: '买入' if x['direction'] > 0 else '卖出', 
            axis=1
        )
        
        # 添加交易原因字段（根据当时的信号）
        orders_df['reason'] = None
        for i, order in orders_df.iterrows():
            timestamp = order['datetime']
            if timestamp in self.signals.index:
                signal_data = self.signals.loc[timestamp]
                price_ratio = signal_data['price_ratio']
                upper_bound = signal_data['upper_bound'] 
                lower_bound = signal_data['lower_bound']
                
                if price_ratio > upper_bound:
                    reason = f"价格比率({price_ratio:.6f})高于上界({upper_bound:.6f})"
                elif price_ratio < lower_bound:
                    reason = f"价格比率({price_ratio:.6f})低于下界({lower_bound:.6f})"
                else:
                    reason = "信号变化或持仓调整"
                    
                orders_df.at[i, 'reason'] = reason
        
        # 保存到CSV
        orders_df.to_csv(filepath, index=False)
        
        if self.verbose:
            print(f"订单记录已保存至: {filepath}")
    
    def analyze_signal_at_time(self, timestamp: Union[str, pd.Timestamp], verbose: bool = True) -> Dict:
        """
        分析特定时间点的信号数据
        
        Args:
            timestamp: 时间戳或字符串格式的时间 (例如 '2024-11-22 10:28:00')
            verbose: 是否打印详细信息
            
        Returns:
            包含该时间点信号数据的字典
        """
        # 如果输入是字符串，转换为Timestamp
        if isinstance(timestamp, str):
            timestamp = pd.Timestamp(timestamp)
        
        # 查找最接近的时间点
        if timestamp not in self.signals.index:
            closest_idx = self.signals.index.get_indexer([timestamp], method='nearest')[0]
            timestamp = self.signals.index[closest_idx]
            if verbose:
                print(f"找到最接近的时间点: {timestamp}")
        
        # 获取该时间点的信号数据
        signal_data = self.signals.loc[timestamp].to_dict()
        
        # 计算额外信息：与上下边界的距离百分比
        price_ratio = signal_data['price_ratio']
        upper_bound = signal_data['upper_bound']
        lower_bound = signal_data['lower_bound']
        
        signal_data['distance_to_upper_pct'] = (upper_bound - price_ratio) / price_ratio * 100 if not pd.isna(upper_bound) else None
        signal_data['distance_to_lower_pct'] = (price_ratio - lower_bound) / price_ratio * 100 if not pd.isna(lower_bound) else None
        
        # 判断是否触发交易信号
        trade_signal = signal_data['trade_signal']
        signal_data['signal_text'] = '无信号' if trade_signal == 0 else ('买入' + self.pairs[0] + ',卖出' + self.pairs[1] if trade_signal == 1 else '卖出' + self.pairs[0] + ',买入' + self.pairs[1])
        
        if verbose:
            etf1, etf2 = self.pairs
            print(f"\n===== {timestamp} 信号分析 =====")
            print(f"ETF对: {etf1} - {etf2}")
            print(f"ETF1 价格: {signal_data['etf1_price']:.4f}")
            print(f"ETF2 价格: {signal_data['etf2_price']:.4f}")
            print(f"价格比率: {price_ratio:.6f}")
            print(f"移动平均(Beta): {signal_data['ma']:.6f}")
            print(f"交易上界: {upper_bound:.6f} (距离当前: {signal_data['distance_to_upper_pct']:.2f}%)")
            print(f"交易下界: {lower_bound:.6f} (距离当前: {signal_data['distance_to_lower_pct']:.2f}%)")
            print(f"标准差: {signal_data['std']:.6f}")
            print(f"交易信号: {signal_data['signal_text']}")
            
            # 计算窗口起始时间（用于说明该信号基于哪个时间段的数据）
            window_size = self.window
            all_timestamps = self.signals.index
            current_idx = all_timestamps.get_loc(timestamp)
            
            if current_idx >= window_size:
                window_start = all_timestamps[current_idx - window_size + 1]
                print(f"\n此信号基于: {window_start} 至 {timestamp} 的数据计算")
        
        return signal_data 