"""
可视化模块，用于生成回测结果的可视化分析
"""

import os
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from datetime import datetime

class BacktestVisualizer:
    """回测结果可视化器"""
    
    def __init__(self, save_dir='results'):
        """
        初始化可视化器
        
        参数:
            save_dir (str): 图表保存目录
        """
        self.save_dir = save_dir
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            
        # 设置绘图样式
        plt.style.use('classic')
        # 设置清晰的配色方案
        self.colors = {
            'etf1': '#1f77b4',  # 深蓝色
            'etf2': '#2ca02c',  # 深绿色
            'theoretical': '#ff7f0e',  # 橙色
            'range': '#ffb6c1',  # 浅红色
            'portfolio': '#7b3294',  # 紫色
            'beta': '#d62728'  # 红色
        }
        
    def plot_all(self, daily_data: pd.DataFrame, trades_data: pd.DataFrame, timestamp: str = None):
        """
        生成可视化图表
        
        参数:
            daily_data (pd.DataFrame): 每日数据
            trades_data (pd.DataFrame): 交易数据
            timestamp (str, optional): 时间戳，用于文件命名
        """
        # 只绘制组合图表
        self.plot_combined_analysis(daily_data, trades_data)
        
    def plot_combined_analysis(self, daily_data: pd.DataFrame, trades_data: pd.DataFrame):
        """
        绘制组合分析图表，包含ETF价格、理论价格、交易区间、超额收益和回归系数
        
        参数:
            daily_data (pd.DataFrame): 每日数据
            trades_data (pd.DataFrame): 交易数据
        """
        # 创建三个子图
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(15, 12), height_ratios=[3, 2, 1], dpi=300)
        
        # 1. ETF价格和交易区间图
        ax1.plot(daily_data['datetime'], daily_data['etf1_price'], 
                color=self.colors['etf1'], label='ETF1 Price', linewidth=1.5)
        ax1.plot(daily_data['datetime'], daily_data['etf2_price'], 
                color=self.colors['etf2'], label='ETF2 Price', linewidth=1.5)
        ax1.plot(daily_data['datetime'], daily_data['theoretical_price'], 
                color=self.colors['theoretical'], label='Theoretical Price', 
                linestyle='--', alpha=0.8, linewidth=1.5)
        
        # 增加交易区间的宽度
        ax1.fill_between(daily_data['datetime'], 
                        daily_data['lower_bound'], 
                        daily_data['upper_bound'], 
                        color=self.colors['range'], alpha=0.8, label='Trading Range',
                        linewidth=4.0)
        ax1.set_ylabel('Price')
        ax1.legend(loc='lower right', framealpha=0.9)
        ax1.grid(True, alpha=0.3)
        
        # 2. 相对于ETF1的超额收益
        # 计算相对收益率
        etf1_returns = daily_data['etf1_price'].pct_change()
        portfolio_returns = daily_data['portfolio_value'].pct_change()
        excess_returns = (1 + portfolio_returns).cumprod() - (1 + etf1_returns).cumprod()
        
        ax2.plot(daily_data['datetime'], excess_returns, 
                color=self.colors['portfolio'], label='Excess Returns vs ETF1', 
                linewidth=1.5)
        ax2.set_ylabel('Excess Returns')
        ax2.legend(loc='lower right', framealpha=0.9)
        ax2.grid(True, alpha=0.3)
        
        # 3. 回归系数
        ax3.plot(daily_data['datetime'], daily_data['beta'], 
                color=self.colors['beta'], label='Beta', linewidth=1.5)
        ax3.fill_between(daily_data['datetime'],
                        daily_data['beta'] - daily_data['std_err'],
                        daily_data['beta'] + daily_data['std_err'],
                        color=self.colors['beta'], alpha=0.2, label='Beta ± Std Err')
        ax3.set_xlabel('Date')
        ax3.set_ylabel('Beta')
        ax3.legend(loc='lower right', framealpha=0.9)
        ax3.grid(True, alpha=0.3)
        
        # 设置总标题
        fig.suptitle('ETF Pairs Trading Strategy Analysis', fontsize=14, y=0.95)
        
        # 调整子图间距
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime('%Y%m%d')
        plt.savefig(os.path.join(self.save_dir, f'strategy_analysis_{timestamp}.png'), 
                   dpi=300, bbox_inches='tight')
        plt.close() 