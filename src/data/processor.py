"""
ETF数据处理模块
负责数据清洗、预处理和统计分析
"""

import os
import logging
import pandas as pd
import numpy as np
from typing import Tuple, Dict, Optional
import matplotlib.pyplot as plt
import seaborn as sns

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ETFDataProcessor:
    """ETF数据处理类"""
    
    def __init__(self, data_dir='data'):
        """
        初始化数据处理器
        
        Parameters
        ----------
        data_dir : str, optional
            数据目录, by default 'data'
        """
        self.data_dir = data_dir
        
    def load_pair_data(self, symbol1: str, symbol2: str) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        加载两个ETF的数据
        
        Parameters
        ----------
        symbol1 : str
            第一个ETF代码
        symbol2 : str
            第二个ETF代码
            
        Returns
        -------
        Tu<PERSON>[pd.DataFrame, pd.DataFrame]
            两个ETF的数据框
        """
        try:
            # 构建文件路径
            file1 = os.path.join(self.data_dir, f'{symbol1}.csv')
            file2 = os.path.join(self.data_dir, f'{symbol2}.csv')
            
            # 读取数据
            df1 = pd.read_csv(file1, index_col='date', parse_dates=True)
            df2 = pd.read_csv(file2, index_col='date', parse_dates=True)
            
            logger.info(f"成功加载 {symbol1} 和 {symbol2} 的数据")
            return df1, df2
            
        except Exception as e:
            logger.error(f"加载数据时发生错误: {str(e)}")
            raise
            
    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        清洗数据
        
        Parameters
        ----------
        df : pd.DataFrame
            原始数据框
            
        Returns
        -------
        pd.DataFrame
            清洗后的数据框
        """
        # 复制数据避免修改原始数据
        df = df.copy()
        
        # 处理缺失值
        df.dropna()  # 向前填充
        
        # 删除重复数据
        df = df[~df.index.duplicated(keep='first')]
        
        '''# 检查异常值（这里使用3倍标准差作为阈值）
        for col in ['open', 'high', 'low', 'close']:
            mean = df[col].mean()
            std = df[col].std()
            df[col] = df[col].clip(mean - 10*std, mean + 10*std)'''
            
        return df
        
    def calculate_returns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算收益率
        
        Parameters
        ----------
        df : pd.DataFrame
            价格数据
            
        Returns
        -------
        pd.DataFrame
            包含收益率的数据框
        """
        df = df.copy()
        
        # 计算日收益率
        df['returns'] = df['close'].pct_change()
        
        # 计算对数收益率
        df['log_returns'] = np.log(df['close']/df['close'].shift(1))
        
        return df
        
    def generate_summary(self, df: pd.DataFrame, symbol: str) -> Dict:
        """
        生成数据统计摘要
        
        Parameters
        ----------
        df : pd.DataFrame
            数据框
        symbol : str
            ETF代码
            
        Returns
        -------
        Dict
            统计摘要
        """
        summary = {
            'symbol': symbol,
            'start_date': df.index.min(),
            'end_date': df.index.max(),
            'trading_days': len(df),
            'price_stats': df['close'].describe().to_dict(),
            'returns_stats': df['returns'].describe().to_dict(),
            'missing_values': df.isnull().sum().to_dict(),
            'volatility': df['returns'].std() * np.sqrt(252)  # 年化波动率
        }
        
        return summary
        
    def plot_price_history(self, df1: pd.DataFrame, df2: pd.DataFrame, 
                          symbol1: str, symbol2: str, save_dir: Optional[str] = None):
        """
        绘制价格历史图表
        
        Parameters
        ----------
        df1 : pd.DataFrame
            第一个ETF的数据
        df2 : pd.DataFrame
            第二个ETF的数据
        symbol1 : str
            第一个ETF代码
        symbol2 : str
            第二个ETF代码
        save_dir : Optional[str], optional
            图表保存目录, by default None
        """
        plt.figure(figsize=(12, 6))
        
        # 绘制价格走势
        plt.subplot(2, 1, 1)
        plt.plot(df1.index, df1['close'], label=symbol1)
        plt.plot(df2.index, df2['close'], label=symbol2)
        plt.title('ETF价格走势对比')
        plt.legend()
        plt.grid(True)
        
        # 绘制收益率对比
        plt.subplot(2, 1, 2)
        plt.plot(df1.index, df1['returns'], label=f'{symbol1} 收益率')
        plt.plot(df2.index, df2['returns'], label=f'{symbol2} 收益率')
        plt.title('ETF收益率对比')
        plt.legend()
        plt.grid(True)
        
        plt.tight_layout()
        
        if save_dir:
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
            plt.savefig(os.path.join(save_dir, 'price_history.png'))
        else:
            plt.show()
            
        plt.close()
        
    def process_pair_data(self, symbol1: str = '518880', symbol2: str = '518850',
                         save_dir: Optional[str] = None) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        处理配对交易数据的完整流程
        
        Parameters
        ----------
        symbol1 : str, optional
            第一个ETF代码, by default '518880'
        symbol2 : str, optional
            第二个ETF代码, by default '518850'
        save_dir : Optional[str], optional
            结果保存目录, by default None
            
        Returns
        -------
        Tuple[pd.DataFrame, pd.DataFrame]
            处理后的两个ETF数据
        """
        # 加载数据
        df1, df2 = self.load_pair_data(symbol1, symbol2)
        
        # 清洗数据
        df1 = self.clean_data(df1)
        df2 = self.clean_data(df2)
        
        # 计算收益率
        df1 = self.calculate_returns(df1)
        df2 = self.calculate_returns(df2)
        
        # 生成统计摘要
        summary1 = self.generate_summary(df1, symbol1)
        summary2 = self.generate_summary(df2, symbol2)
        
        # 如果指定了保存目录，保存统计结果
        if save_dir:
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
                
            # 保存统计摘要
            pd.DataFrame([summary1, summary2]).to_csv(
                os.path.join(save_dir, 'summary_stats.csv'))
            
            # 绘制价格历史图表
            self.plot_price_history(df1, df2, symbol1, symbol2, save_dir)
            
        return df1, df2

def main():
    """主函数，用于测试"""
    processor = ETFDataProcessor()
    
    # 处理数据并保存结果
    df1, df2 = processor.process_pair_data(save_dir='results')
    
    # 打印基本信息
    print("\n处理后的数据信息:")
    print("\n518880:")
    print(df1.info())
    print("\n518850:")
    print(df2.info())
    
if __name__ == '__main__':
    main() 