"""
ETF数据获取模块
负责从akshare获取ETF数据，并保存到本地
"""

import os
import logging
from datetime import datetime, timedelta
import pandas as pd
import akshare as ak

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ETFDataFetcher:
    """ETF数据获取类"""
    
    def __init__(self, save_dir='data'):
        """
        初始化数据获取器
        
        Parameters
        ----------
        save_dir : str, optional
            数据保存目录, by default 'data'
        """
        self.save_dir = save_dir
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
            
    def fetch_etf_data(self, symbol, start_date=None, end_date=None, frequency='daily'):
        """
        获取ETF历史数据
        
        Parameters
        ----------
        symbol : str
            ETF代码，如'518880'
        start_date : str, optional
            开始日期，格式：'YYYYMMDD', by default None
        end_date : str, optional
            结束日期，格式：'YYYYMMDD', by default None
        frequency : str, optional
            数据频率，'daily'或'minute', by default 'daily'
            
        Returns
        -------
        pd.DataFrame
            ETF历史数据
        """
        try:
            logger.info(f"开始获取ETF {symbol} 的{frequency}数据...")
            
            # 如果未指定日期，默认获取近一年数据
            if start_date is None:
                start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
            if end_date is None:
                end_date = datetime.now().strftime('%Y%m%d')
                
            # 根据频率选择不同的数据获取方法
            if frequency == 'minute':
                df = self._fetch_minute_data(symbol, start_date, end_date)
            else:  # daily
                df = self._fetch_daily_data(symbol, start_date, end_date)
            
            logger.info(f"成功获取原始数据，共 {len(df)} 条记录")
            logger.info(f"数据列名: {df.columns.tolist()}")
            logger.info(f"数据示例:\n{df.head()}")
            
            # 数据清洗和格式化
            df = self._process_data(df, frequency)
            
            # 保存数据
            self._save_data(df, symbol)
            
            logger.info(f"ETF {symbol} {frequency}数据获取成功")
            return df
            
        except Exception as e:
            logger.error(f"获取ETF {symbol} {frequency}数据时发生错误: {str(e)}")
            raise
            
    def _fetch_daily_data(self, symbol, start_date, end_date):
        """获取日线数据"""
        return ak.fund_etf_hist_em(
            symbol=symbol,
            period="daily",
            start_date=start_date,
            end_date=end_date
        )
        
    def _fetch_minute_data(self, symbol, start_date, end_date):
        """获取分钟线数据"""
        # 使用akshare的分钟数据接口
        df = ak.fund_etf_hist_min_em(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            period='1'  # 1分钟
        )
        return df
            
    def _process_data(self, df, frequency='daily'):
        """
        处理原始数据
        
        Parameters
        ----------
        df : pd.DataFrame
            原始数据
        frequency : str
            数据频率，'daily'或'minute'
        
        Returns
        -------
        pd.DataFrame
            处理后的数据
        """
        # 复制数据避免修改原始数据
        df = df.copy()
        
        # 检查数据列
        logger.info("开始处理数据...")
        logger.info(f"原始数据列: {df.columns.tolist()}")
        
        # 根据频率选择不同的列映射
        if frequency == 'minute':
            column_mapping = {
                '时间': 'datetime',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            }
        else:  # daily
            column_mapping = {
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            }
        
        # 重命名列
        df.rename(columns=column_mapping, inplace=True)
        
        # 处理时间列
        if frequency == 'minute':
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
        else:
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)
        
        # 按时间排序
        df.sort_index(inplace=True)
        
        # 确保所有数值列都是float类型
        numeric_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
                
        # 删除不需要的列
        if 'amount' in df.columns:
            df.drop('amount', axis=1, inplace=True)
            
        # 对于分钟数据，只保留交易时间段的数据
        if frequency == 'minute':
            # 创建交易时间掩码
            trading_mask = (
                ((df.index.hour == 9) & (df.index.minute >= 30)) |  # 9:30-10:00
                (df.index.hour == 10) |                            # 10:00-11:00
                (df.index.hour == 11) |                            # 11:00-12:00
                (df.index.hour == 13) |                            # 13:00-14:00
                (df.index.hour == 14) |                            # 14:00-15:00
                ((df.index.hour == 15) & (df.index.minute == 0))   # 15:00
            )
            df = df[trading_mask]
            
        logger.info(f"处理后的数据列: {df.columns.tolist()}")
        logger.info(f"处理后的数据示例:\n{df.head()}")
        
        return df
    
    def _save_data(self, df, symbol):
        """
        保存数据到本地
        
        Parameters
        ----------
        df : pd.DataFrame
            数据
        symbol : str
            ETF代码
        """
        # 构建保存路径
        file_path = os.path.join(self.save_dir, f'{symbol}.csv')
        
        # 保存为CSV文件
        df.to_csv(file_path)
        logger.info(f"数据已保存到 {file_path}")
        
    def get_pairs_data(self, symbol1='518880', symbol2='518850', 
                      start_date=None, end_date=None, frequency='daily'):
        """
        获取两个ETF的配对数据
        
        Parameters
        ----------
        symbol1 : str, optional
            第一个ETF代码, by default '518880'
        symbol2 : str, optional
            第二个ETF代码, by default '518850'
        start_date : str, optional
            开始日期，格式：'YYYYMMDD', by default None
        end_date : str, optional
            结束日期，格式：'YYYYMMDD', by default None
        frequency : str, optional
            数据频率，'daily'或'minute', by default 'daily'
            
        Returns
        -------
        tuple
            (df1, df2) 两个ETF的数据
        """
        # 获取两个ETF的数据
        df1 = self.fetch_etf_data(symbol1, start_date, end_date, frequency)
        df2 = self.fetch_etf_data(symbol2, start_date, end_date, frequency)
        
        # 确保两个数据集的时间对齐
        common_times = df1.index.intersection(df2.index)
        df1 = df1.loc[common_times]
        df2 = df2.loc[common_times]
        
        return df1, df2

def main():
    """主函数，用于测试"""
    fetcher = ETFDataFetcher()
    
    # 测试日线数据
    print("\n获取日线数据...")
    df1_daily, df2_daily = fetcher.get_pairs_data(
        start_date='20230101',
        end_date='20240101',
        frequency='daily'
    )
    
    # 测试分钟线数据
    print("\n获取分钟线数据...")
    df1_minute, df2_minute = fetcher.get_pairs_data(
        start_date='20240101',
        end_date='20240102',
        frequency='minute'
    )
    
    # 打印数据信息
    print("\n日线数据信息:")
    print("518880:")
    print(df1_daily.info())
    print("\n518850:")
    print(df2_daily.info())
    
    print("\n分钟线数据信息:")
    print("518880:")
    print(df1_minute.info())
    print("\n518850:")
    print(df2_minute.info())
    
if __name__ == '__main__':
    main() 