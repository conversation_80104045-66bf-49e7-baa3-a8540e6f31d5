import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Union
import os
from chinese_calendar import is_workday
from datetime import datetime, timedelta

class DataLoader:
    """数据加载器"""
    
    def __init__(self):
        """初始化数据加载器"""
        self.data_dir = 'data'
        
    def is_trading_day(self, date: datetime) -> bool:
        """
        判断是否为交易日
        
        Args:
            date: 日期
            
        Returns:
            bool: 是否为交易日
        """
        # 如果是周末，直接返回False
        if date.weekday() >= 5:
            return False
            
        # 使用chinese_calendar判断是否为工作日
        return is_workday(date)
        
    def get_valid_trading_days(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """
        获取指定日期范围内的有效交易日
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            List[datetime]: 有效交易日列表
        """
        trading_days = []
        current_date = start_date
        
        while current_date <= end_date:
            if self.is_trading_day(current_date):
                trading_days.append(current_date)
            current_date += timedelta(days=1)
            
        return trading_days
        
    def get_next_trading_day(self, date: datetime) -> datetime:
        """
        获取下一个交易日
        
        Args:
            date: 当前日期
            
        Returns:
            datetime: 下一个交易日
        """
        next_day = date + timedelta(days=1)
        while not self.is_trading_day(next_day):
            next_day += timedelta(days=1)
        return next_day
        
    def get_prev_trading_day(self, date: datetime) -> datetime:
        """
        获取上一个交易日
        
        Args:
            date: 当前日期
            
        Returns:
            datetime: 上一个交易日
        """
        prev_day = date - timedelta(days=1)
        while not self.is_trading_day(prev_day):
            prev_day -= timedelta(days=1)
        return prev_day
        
    def check_and_fetch_data(self, start_date: str, end_date: str) -> bool:
        """
        检查并获取数据，优先使用本地数据，必要时才从网络获取
        考虑到非交易日的情况，对日期范围判断进行特殊处理
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            bool: 是否更新了数据
        """
        from src.data.fetcher import ETFDataFetcher
        
        data_files = ['518880.csv', '518850.csv']
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 转换日期格式
        required_start = pd.to_datetime(start_date)
        required_end = pd.to_datetime(end_date)
        
        # 获取有效交易日范围
        trading_days = self.get_valid_trading_days(required_start, required_end)
        if not trading_days:
            raise ValueError("指定日期范围内没有有效交易日")
            
        valid_start = trading_days[0]
        valid_end = trading_days[-1]
        
        print(f"有效交易日范围: {valid_start.date()} 到 {valid_end.date()}")
        
        # 检查每个数据文件
        need_update = False
        local_data_ranges = {}  # 记录本地数据的日期范围
        
        for file in data_files:
            file_path = os.path.join(self.data_dir, file)
            symbol = os.path.splitext(file)[0]
            
            if not os.path.exists(file_path):
                print(f"未找到数据文件: {file}")
                need_update = True
                continue
                
            # 尝试读取本地文件
            try:
                # 尝试不同的编码方式
                for encoding in ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']:
                    try:
                        # 读取整个数据文件的日期列
                        df_dates = pd.read_csv(
                            file_path,
                            usecols=['date'],
                            encoding=encoding
                        )
                        dates = pd.to_datetime(df_dates['date'])
                        
                        local_data_ranges[symbol] = {
                            'start': dates.min(),
                            'end': dates.max(),
                            'encoding': encoding
                        }
                        print(f"{symbol} 本地数据范围: {dates.min().date()} 到 {dates.max().date()}")
                        break
                    except Exception:
                        continue
                        
                if symbol not in local_data_ranges:
                    print(f"无法读取数据文件: {file}")
                    need_update = True
                    
            except Exception as e:
                print(f"检查数据文件 {file} 时出错: {str(e)}")
                need_update = True
        
        # 检查是否需要更新数据
        if need_update:
            print("\n文件缺失或损坏，需要更新数据")
        elif len(local_data_ranges) == len(data_files):  # 确保所有文件都被成功读取
            # 检查所有文件的数据范围是否满足需求
            all_data_sufficient = all(
                data['start'] <= valid_start and data['end'] >= valid_end
                for data in local_data_ranges.values()
            )
            if all_data_sufficient:
                print("\n本地数据范围满足需求，使用本地数据文件")
                return False
            else:
                print("\n本地数据范围不足，需要更新数据")
        
        # 如果需要更新数据，执行更新
        print("从网络获取数据...")
        fetcher = ETFDataFetcher()
        
        # 获取所需的数据
        df1, df2 = fetcher.get_pairs_data(
            symbol1='518880',
            symbol2='518850',
            start_date=start_date.replace('-', ''),
            end_date=end_date.replace('-', '')
        )
        
        # 保存数据到本地
        print("保存数据到本地...")
        for symbol, df in [('518880', df1), ('518850', df2)]:
            file_path = os.path.join(self.data_dir, f"{symbol}.csv")
            
            # 如果本地已有数据，合并新旧数据
            if symbol in local_data_ranges:
                try:
                    # 读取现有数据
                    old_df = pd.read_csv(
                        file_path,
                        encoding=local_data_ranges[symbol]['encoding']
                    )
                    old_df['date'] = pd.to_datetime(old_df['date'])
                    old_df.set_index('date', inplace=True)
                    
                    # 合并数据并去重
                    df = pd.concat([old_df, df])
                    df = df[~df.index.duplicated(keep='last')]
                    
                    # 只保留交易日数据
                    df = df[df.index.map(self.is_trading_day)]
                    df.sort_index(inplace=True)
                    
                    print(f"合并 {symbol} 数据: {df.index[0].date()} 到 {df.index[-1].date()}")
                except Exception as e:
                    print(f"合并数据时出错: {str(e)}")
            
            # 保存到文件
            df.to_csv(file_path, encoding='utf-8')
            print(f"保存 {symbol} 数据文件")
        
        return True
        
    def load_data(
        self,
        start_date: str,
        end_date: str,
        frequency: str = 'minute'
    ) -> pd.DataFrame:
        """
        加载数据
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            frequency: 数据频率 ('day', 'minute', 'tick')
        Returns:
            包含所有ETF数据的DataFrame
        """
        # 验证日期格式和范围
        start_datetime = pd.to_datetime(start_date)
        end_datetime = pd.to_datetime(end_date)
        if end_datetime < start_datetime:
            raise ValueError("结束日期不能早于开始日期")
            
        # 验证频率参数
        if frequency not in ['day', 'minute', 'tick']:
            raise ValueError(f"不支持的数据频率: {frequency}")
            
        # 获取数据文件列表
        data_files = [f for f in os.listdir(self.data_dir) if f.startswith('hq-shkl-')]
        
        if not data_files:
            raise ValueError(f"在{self.data_dir}目录下未找到数据文件")
        # 读取所有数据文件
        merged_df = None
        for file in data_files:
            file_path = os.path.join(self.data_dir, file)
            
            # 尝试不同的编码方式读取文件
            encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    
                    # 提取ETF代码
                    symbol = df['securityid'].iloc[0]
                    
                    # 合并日期和时间列
                    df['datetime'] = pd.to_datetime(
                        df['date'].astype(str) + ' ' + 
                        df['time'].astype(str).str.zfill(9).str[:4],
                        format='%Y%m%d %H%M'
                    )
                    df.set_index('datetime', inplace=True)
                    
                    # 将价格除以10000并只保留需要的列
                    df['open'] = df['open'] / 10000
                    df = df[['open']].rename(columns={'open': symbol})
                    
                    print(f"成功使用 {encoding} 编码读取文件: {file}")
                    break
                except Exception as e:
                    continue
            
            if df is None:
                raise ValueError(f"无法读取数据文件: {file}")
            
            # 过滤日期范围
            df = df.loc[start_datetime:end_datetime]
            
            # 只在交易时间内（9:30-15:00）
            df = df.between_time('09:30', '15:00')
            
            # 使用merge合并数据
            if merged_df is None:
                merged_df = df
            else:
                merged_df = merged_df.merge(df, how='inner', left_index=True, right_index=True)
        
        # 处理缺失值
        merged_df = merged_df.dropna()
        
        # 确保只有两个ETF的数据
        if len(merged_df.columns) != 2:
            raise ValueError(f"需要恰好两个ETF的数据，但找到了{len(merged_df.columns)}个: {list(merged_df.columns)}")
        
        return merged_df
    
