#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ETF配对交易策略深度统计分析脚本

实现两项核心分析：
1. 价差序列统计特性分析（白噪声检验、分布特征、正态性检验等）
2. 回归参数时变性分析（beta系数和标准误差的时变规律）

作者: ETF配对交易分析系统
创建时间: 2025-07-07
"""

import os
import sys
import warnings
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import matplotlib.font_manager as fm

# 统计检验相关
from scipy import stats
from scipy.stats import jarque_bera, shapiro, kstest, normaltest
from statsmodels.stats.diagnostic import acorr_ljungbox
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.regression.rolling import RollingOLS
# from arch.unitroot import ADF, KPSS  # 可选导入

# 忽略警告
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class ETFStatisticalAnalyzer:
    """ETF配对交易策略统计分析器"""
    
    def __init__(self, output_dir: str = "statistical_analysis_results"):
        """
        初始化分析器
        
        Args:
            output_dir: 结果输出目录
        """
        self.output_dir = output_dir
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 数据存储
        self.price_data = None
        self.signals_data = None
        self.spread_series = None
        self.beta_series = None
        self.std_error_series = None
        
        # 分析结果存储
        self.analysis_results = {
            'spread_analysis': {},
            'parameter_analysis': {},
            'optimization_suggestions': {}
        }
        
        print(f"ETF统计分析器初始化完成，结果将保存到: {self.output_dir}")
    
    def load_signals_data(self, signals_file_path: str) -> pd.DataFrame:
        """
        加载信号数据文件
        
        Args:
            signals_file_path: 信号文件路径
            
        Returns:
            加载的信号数据
        """
        try:
            print(f"正在加载信号数据: {signals_file_path}")
            
            # 读取CSV文件
            signals_df = pd.read_csv(signals_file_path)
            
            # 转换时间列
            if 'datetime' in signals_df.columns:
                signals_df['datetime'] = pd.to_datetime(signals_df['datetime'])
                signals_df.set_index('datetime', inplace=True)
            
            # 过滤掉无效数据行
            signals_df = signals_df.dropna(subset=['ma', 'std', 'beta'])
            
            print(f"信号数据加载完成: {len(signals_df)} 行有效数据")
            print(f"数据列: {signals_df.columns.tolist()}")
            print(f"时间范围: {signals_df.index[0]} 至 {signals_df.index[-1]}")
            
            self.signals_data = signals_df
            return signals_df
            
        except Exception as e:
            print(f"加载信号数据时出错: {str(e)}")
            raise
    
    def calculate_spread_series(self) -> pd.Series:
        """
        根据信号数据计算价差序列
        价差 = etf1_price - ma * etf2_price - 0.5 * std
        
        Returns:
            价差序列
        """
        if self.signals_data is None:
            raise ValueError("请先加载信号数据")
        
        print("\n=== 计算价差序列 ===")
        
        # 提取必要的列
        etf1_price = self.signals_data['etf1_price']
        etf2_price = self.signals_data['etf2_price'] 
        ma = self.signals_data['ma']  # 这里ma实际上是beta值
        std = self.signals_data['std']  # 这里是beta的标准误差
        
        # 计算价差序列：etf1 - ma*etf2 - 0.5*std
        spread_series = etf1_price - ma * etf2_price - 0.5 * std
        
        # 去除无效值
        spread_series = spread_series.dropna()
        
        print(f"价差序列计算完成: {len(spread_series)} 个有效数据点")
        print(f"价差统计: 均值={spread_series.mean():.6f}, 标准差={spread_series.std():.6f}")
        print(f"价差范围: [{spread_series.min():.6f}, {spread_series.max():.6f}]")
        
        self.spread_series = spread_series
        self.beta_series = ma.dropna()
        self.std_error_series = std.dropna()
        
        return spread_series
    
    def white_noise_tests(self) -> Dict:
        """
        对价差序列进行白噪声检验
        
        Returns:
            白噪声检验结果
        """
        if self.spread_series is None:
            raise ValueError("请先计算价差序列")
        
        print("\n=== 价差序列白噪声检验 ===")
        results = {}
        
        # 1. Ljung-Box检验（检验序列相关性）
        print("1. Ljung-Box检验（序列相关性）")
        try:
            # 检验不同滞后期
            lags_to_test = [10, 20, 30]
            ljung_box_results = {}
            
            for lag in lags_to_test:
                if len(self.spread_series) > lag:
                    lb_result = acorr_ljungbox(self.spread_series, lags=lag, return_df=True)
                    # 取最后一行（最大滞后期）的结果
                    lb_stat = lb_result['lb_stat'].iloc[-1]
                    lb_pvalue = lb_result['lb_pvalue'].iloc[-1]
                    
                    ljung_box_results[f'lag_{lag}'] = {
                        'statistic': float(lb_stat),
                        'p_value': float(lb_pvalue),
                        'is_white_noise': lb_pvalue > 0.05
                    }
                    
                    print(f"   滞后{lag}期: 统计量={lb_stat:.4f}, p值={lb_pvalue:.4f}, "
                          f"白噪声={'是' if lb_pvalue > 0.05 else '否'}")
            
            results['ljung_box'] = ljung_box_results
            
        except Exception as e:
            print(f"   Ljung-Box检验失败: {str(e)}")
        
        # 2. ADF平稳性检验
        print("2. ADF平稳性检验")
        try:
            adf_result = adfuller(self.spread_series.dropna())
            adf_stat, adf_pvalue = adf_result[0], adf_result[1]
            
            results['adf_test'] = {
                'statistic': float(adf_stat),
                'p_value': float(adf_pvalue),
                'critical_values': {str(k): float(v) for k, v in adf_result[4].items()},
                'is_stationary': adf_pvalue < 0.05
            }
            
            print(f"   ADF统计量: {adf_stat:.4f}")
            print(f"   p值: {adf_pvalue:.4f}")
            print(f"   平稳性: {'是' if adf_pvalue < 0.05 else '否'}")
            
        except Exception as e:
            print(f"   ADF检验失败: {str(e)}")
        
        # 3. KPSS平稳性检验
        print("3. KPSS平稳性检验")
        try:
            kpss_result = kpss(self.spread_series.dropna())
            kpss_stat, kpss_pvalue = kpss_result[0], kpss_result[1]
            
            results['kpss_test'] = {
                'statistic': float(kpss_stat),
                'p_value': float(kpss_pvalue),
                'critical_values': {str(k): float(v) for k, v in kpss_result[3].items()},
                'is_stationary': kpss_pvalue > 0.05
            }
            
            print(f"   KPSS统计量: {kpss_stat:.4f}")
            print(f"   p值: {kpss_pvalue:.4f}")
            print(f"   平稳性: {'是' if kpss_pvalue > 0.05 else '否'}")
            
        except Exception as e:
            print(f"   KPSS检验失败: {str(e)}")
        
        self.analysis_results['spread_analysis']['white_noise_tests'] = results
        return results
    
    def distribution_analysis(self) -> Dict:
        """
        分析价差序列的分布特征
        
        Returns:
            分布分析结果
        """
        if self.spread_series is None:
            raise ValueError("请先计算价差序列")
        
        print("\n=== 价差序列分布特征分析 ===")
        results = {}
        
        # 1. 描述性统计
        print("1. 描述性统计")
        desc_stats = self.spread_series.describe()
        skewness = stats.skew(self.spread_series.dropna())
        kurtosis = stats.kurtosis(self.spread_series.dropna())
        
        results['descriptive_stats'] = {
            'count': int(desc_stats['count']),
            'mean': float(desc_stats['mean']),
            'std': float(desc_stats['std']),
            'min': float(desc_stats['min']),
            'max': float(desc_stats['max']),
            'skewness': float(skewness),
            'kurtosis': float(kurtosis),
            'median': float(desc_stats['50%']),
            'q25': float(desc_stats['25%']),
            'q75': float(desc_stats['75%'])
        }
        
        print(f"   样本数量: {desc_stats['count']}")
        print(f"   均值: {desc_stats['mean']:.6f}")
        print(f"   标准差: {desc_stats['std']:.6f}")
        print(f"   偏度: {skewness:.4f}")
        print(f"   峰度: {kurtosis:.4f}")
        
        # 2. 正态性检验
        print("2. 正态性检验")
        normality_tests = {}
        
        # Shapiro-Wilk检验（适用于小样本）
        if len(self.spread_series) <= 5000:
            try:
                sw_stat, sw_pvalue = shapiro(self.spread_series.dropna())
                normality_tests['shapiro_wilk'] = {
                    'statistic': float(sw_stat),
                    'p_value': float(sw_pvalue),
                    'is_normal': sw_pvalue > 0.05
                }
                print(f"   Shapiro-Wilk: 统计量={sw_stat:.4f}, p值={sw_pvalue:.4f}, "
                      f"正态分布={'是' if sw_pvalue > 0.05 else '否'}")
            except Exception as e:
                print(f"   Shapiro-Wilk检验失败: {str(e)}")
        
        # Kolmogorov-Smirnov检验
        try:
            # 与标准正态分布比较
            mean, std = self.spread_series.mean(), self.spread_series.std()
            normalized_data = (self.spread_series - mean) / std
            ks_stat, ks_pvalue = kstest(normalized_data, 'norm')
            
            normality_tests['kolmogorov_smirnov'] = {
                'statistic': float(ks_stat),
                'p_value': float(ks_pvalue),
                'is_normal': ks_pvalue > 0.05
            }
            print(f"   Kolmogorov-Smirnov: 统计量={ks_stat:.4f}, p值={ks_pvalue:.4f}, "
                  f"正态分布={'是' if ks_pvalue > 0.05 else '否'}")
        except Exception as e:
            print(f"   Kolmogorov-Smirnov检验失败: {str(e)}")
        
        # Jarque-Bera检验
        try:
            jb_stat, jb_pvalue = jarque_bera(self.spread_series.dropna())
            normality_tests['jarque_bera'] = {
                'statistic': float(jb_stat),
                'p_value': float(jb_pvalue),
                'is_normal': jb_pvalue > 0.05
            }
            print(f"   Jarque-Bera: 统计量={jb_stat:.4f}, p值={jb_pvalue:.4f}, "
                  f"正态分布={'是' if jb_pvalue > 0.05 else '否'}")
        except Exception as e:
            print(f"   Jarque-Bera检验失败: {str(e)}")
        
        results['normality_tests'] = normality_tests

        self.analysis_results['spread_analysis']['distribution_analysis'] = results
        return results

    def autocorrelation_analysis(self) -> Dict:
        """
        分析价差序列的自相关性和聚集性特征

        Returns:
            自相关分析结果
        """
        if self.spread_series is None:
            raise ValueError("请先计算价差序列")

        print("\n=== 价差序列自相关性分析 ===")
        results = {}

        # 1. 计算自相关函数
        print("1. 自相关函数分析")
        max_lags = min(50, len(self.spread_series) // 4)

        try:
            from statsmodels.tsa.stattools import acf, pacf

            # 自相关函数
            acf_values, acf_confint = acf(self.spread_series.dropna(),
                                         nlags=max_lags,
                                         alpha=0.05)

            # 偏自相关函数
            pacf_values, pacf_confint = pacf(self.spread_series.dropna(),
                                            nlags=max_lags,
                                            alpha=0.05)

            results['autocorrelation'] = {
                'acf_values': acf_values.tolist(),
                'pacf_values': pacf_values.tolist(),
                'significant_lags_acf': [],
                'significant_lags_pacf': []
            }

            # 检查显著的自相关
            for i in range(1, len(acf_values)):
                if abs(acf_values[i]) > abs(acf_confint[i, 1] - acf_values[i]):
                    results['autocorrelation']['significant_lags_acf'].append(i)

            for i in range(1, len(pacf_values)):
                if abs(pacf_values[i]) > abs(pacf_confint[i, 1] - pacf_values[i]):
                    results['autocorrelation']['significant_lags_pacf'].append(i)

            print(f"   显著自相关滞后期: {results['autocorrelation']['significant_lags_acf'][:10]}")
            print(f"   显著偏自相关滞后期: {results['autocorrelation']['significant_lags_pacf'][:10]}")

        except Exception as e:
            print(f"   自相关分析失败: {str(e)}")

        # 2. 聚集性分析（波动率聚集）
        print("2. 波动率聚集性分析")
        try:
            # 计算绝对收益率
            abs_returns = np.abs(self.spread_series.diff().dropna())

            # 对绝对收益率进行Ljung-Box检验
            if len(abs_returns) > 10:
                lb_result = acorr_ljungbox(abs_returns, lags=10, return_df=True)
                lb_stat = lb_result['lb_stat'].iloc[-1]
                lb_pvalue = lb_result['lb_pvalue'].iloc[-1]

                results['volatility_clustering'] = {
                    'ljung_box_stat': float(lb_stat),
                    'ljung_box_pvalue': float(lb_pvalue),
                    'has_clustering': lb_pvalue < 0.05
                }

                print(f"   波动率聚集检验: 统计量={lb_stat:.4f}, p值={lb_pvalue:.4f}")
                print(f"   存在波动率聚集: {'是' if lb_pvalue < 0.05 else '否'}")

        except Exception as e:
            print(f"   波动率聚集分析失败: {str(e)}")

        self.analysis_results['spread_analysis']['autocorrelation_analysis'] = results
        return results

    def parameter_stability_analysis(self) -> Dict:
        """
        分析回归参数（beta和标准误差）的时变性和稳定性

        Returns:
            参数稳定性分析结果
        """
        if self.beta_series is None or self.std_error_series is None:
            raise ValueError("请先计算价差序列以获取参数序列")

        print("\n=== 回归参数时变性分析 ===")
        results = {}

        # 1. Beta系数分析
        print("1. Beta系数时变特征")
        beta_stats = {
            'mean': float(self.beta_series.mean()),
            'std': float(self.beta_series.std()),
            'min': float(self.beta_series.min()),
            'max': float(self.beta_series.max()),
            'cv': float(self.beta_series.std() / self.beta_series.mean()),  # 变异系数
            'range': float(self.beta_series.max() - self.beta_series.min())
        }

        print(f"   Beta均值: {beta_stats['mean']:.6f}")
        print(f"   Beta标准差: {beta_stats['std']:.6f}")
        print(f"   Beta变异系数: {beta_stats['cv']:.4f}")
        print(f"   Beta变化范围: {beta_stats['range']:.6f}")

        # Beta的平稳性检验
        try:
            adf_result = adfuller(self.beta_series.dropna())
            beta_stats['adf_statistic'] = float(adf_result[0])
            beta_stats['adf_pvalue'] = float(adf_result[1])
            beta_stats['is_stationary'] = adf_result[1] < 0.05

            print(f"   Beta平稳性(ADF): p值={adf_result[1]:.4f}, "
                  f"平稳={'是' if adf_result[1] < 0.05 else '否'}")
        except Exception as e:
            print(f"   Beta平稳性检验失败: {str(e)}")

        results['beta_analysis'] = beta_stats

        # 2. 标准误差分析
        print("2. 标准误差时变特征")
        std_error_stats = {
            'mean': float(self.std_error_series.mean()),
            'std': float(self.std_error_series.std()),
            'min': float(self.std_error_series.min()),
            'max': float(self.std_error_series.max()),
            'cv': float(self.std_error_series.std() / self.std_error_series.mean()),
            'range': float(self.std_error_series.max() - self.std_error_series.min())
        }

        print(f"   标准误差均值: {std_error_stats['mean']:.6f}")
        print(f"   标准误差标准差: {std_error_stats['std']:.6f}")
        print(f"   标准误差变异系数: {std_error_stats['cv']:.4f}")
        print(f"   标准误差变化范围: {std_error_stats['range']:.6f}")

        # 标准误差的平稳性检验
        try:
            adf_result = adfuller(self.std_error_series.dropna())
            std_error_stats['adf_statistic'] = float(adf_result[0])
            std_error_stats['adf_pvalue'] = float(adf_result[1])
            std_error_stats['is_stationary'] = adf_result[1] < 0.05

            print(f"   标准误差平稳性(ADF): p值={adf_result[1]:.4f}, "
                  f"平稳={'是' if adf_result[1] < 0.05 else '否'}")
        except Exception as e:
            print(f"   标准误差平稳性检验失败: {str(e)}")

        results['std_error_analysis'] = std_error_stats

        # 3. 参数变化率分析
        print("3. 参数变化率分析")
        try:
            # 计算参数的变化率
            beta_changes = self.beta_series.pct_change().dropna()
            std_error_changes = self.std_error_series.pct_change().dropna()

            change_stats = {
                'beta_change_mean': float(beta_changes.mean()),
                'beta_change_std': float(beta_changes.std()),
                'beta_change_max_abs': float(beta_changes.abs().max()),
                'std_error_change_mean': float(std_error_changes.mean()),
                'std_error_change_std': float(std_error_changes.std()),
                'std_error_change_max_abs': float(std_error_changes.abs().max())
            }

            print(f"   Beta平均变化率: {change_stats['beta_change_mean']:.6f}")
            print(f"   Beta最大绝对变化率: {change_stats['beta_change_max_abs']:.6f}")
            print(f"   标准误差平均变化率: {change_stats['std_error_change_mean']:.6f}")
            print(f"   标准误差最大绝对变化率: {change_stats['std_error_change_max_abs']:.6f}")

            results['parameter_changes'] = change_stats

        except Exception as e:
            print(f"   参数变化率分析失败: {str(e)}")

        # 4. 滚动窗口稳定性分析
        print("4. 滚动窗口稳定性分析")
        try:
            # 计算不同窗口大小下的参数稳定性
            window_sizes = [60, 120, 240]  # 1小时、2小时、4小时
            stability_results = {}

            for window in window_sizes:
                if len(self.beta_series) > window:
                    # 计算滚动标准差
                    beta_rolling_std = self.beta_series.rolling(window=window).std()
                    std_error_rolling_std = self.std_error_series.rolling(window=window).std()

                    stability_results[f'window_{window}'] = {
                        'beta_rolling_std_mean': float(beta_rolling_std.mean()),
                        'std_error_rolling_std_mean': float(std_error_rolling_std.mean())
                    }

                    print(f"   窗口{window}: Beta滚动标准差均值={beta_rolling_std.mean():.6f}, "
                          f"标准误差滚动标准差均值={std_error_rolling_std.mean():.6f}")

            results['stability_analysis'] = stability_results

        except Exception as e:
            print(f"   滚动窗口稳定性分析失败: {str(e)}")

        self.analysis_results['parameter_analysis'] = results
        return results

    def generate_optimization_suggestions(self) -> Dict:
        """
        基于分析结果生成优化建议

        Returns:
            优化建议
        """
        print("\n=== 生成优化建议 ===")
        suggestions = {}

        # 1. 参数更新频率建议
        if 'parameter_analysis' in self.analysis_results:
            param_analysis = self.analysis_results['parameter_analysis']

            # 基于参数稳定性建议更新频率
            if 'beta_analysis' in param_analysis:
                beta_cv = param_analysis['beta_analysis'].get('cv', 0)

                if beta_cv < 0.01:  # 变异系数很小，参数很稳定
                    update_freq = "5-10分钟"
                    reason = "Beta系数非常稳定，可以降低更新频率"
                elif beta_cv < 0.05:  # 变异系数较小
                    update_freq = "2-5分钟"
                    reason = "Beta系数较稳定，可以适当降低更新频率"
                else:  # 变异系数较大
                    update_freq = "1分钟"
                    reason = "Beta系数变化较大，建议保持高频更新"

                suggestions['parameter_update_frequency'] = {
                    'recommended_frequency': update_freq,
                    'reason': reason,
                    'beta_cv': beta_cv
                }

                print(f"   参数更新频率建议: {update_freq}")
                print(f"   理由: {reason}")

        # 2. 交易信号优化建议
        if 'spread_analysis' in self.analysis_results:
            spread_analysis = self.analysis_results['spread_analysis']

            # 基于白噪声检验结果
            if 'white_noise_tests' in spread_analysis:
                white_noise = spread_analysis['white_noise_tests']

                # 检查是否为白噪声
                is_white_noise = True
                if 'ljung_box' in white_noise:
                    for lag_result in white_noise['ljung_box'].values():
                        if not lag_result.get('is_white_noise', True):
                            is_white_noise = False
                            break

                if is_white_noise:
                    signal_suggestion = "当前价差序列接近白噪声，交易信号质量较好"
                else:
                    signal_suggestion = "价差序列存在序列相关性，建议考虑增加滞后项或调整策略参数"

                suggestions['signal_quality'] = {
                    'assessment': signal_suggestion,
                    'is_white_noise': is_white_noise
                }

                print(f"   信号质量评估: {signal_suggestion}")

            # 基于分布特征
            if 'distribution_analysis' in spread_analysis:
                dist_analysis = spread_analysis['distribution_analysis']

                if 'descriptive_stats' in dist_analysis:
                    skewness = dist_analysis['descriptive_stats'].get('skewness', 0)
                    kurtosis = dist_analysis['descriptive_stats'].get('kurtosis', 0)

                    distribution_suggestions = []

                    if abs(skewness) > 1:
                        distribution_suggestions.append(f"价差分布偏度较大({skewness:.2f})，建议考虑非对称交易区间")

                    if kurtosis > 3:
                        distribution_suggestions.append(f"价差分布峰度较高({kurtosis:.2f})，存在厚尾特征，建议调整风险管理参数")

                    if not distribution_suggestions:
                        distribution_suggestions.append("价差分布特征良好，当前参数设置合理")

                    suggestions['distribution_optimization'] = {
                        'suggestions': distribution_suggestions,
                        'skewness': skewness,
                        'kurtosis': kurtosis
                    }

                    for suggestion in distribution_suggestions:
                        print(f"   分布优化建议: {suggestion}")

        # 3. 风险管理建议
        risk_suggestions = []

        # 基于波动率聚集
        if ('spread_analysis' in self.analysis_results and
            'autocorrelation_analysis' in self.analysis_results['spread_analysis'] and
            'volatility_clustering' in self.analysis_results['spread_analysis']['autocorrelation_analysis']):

            vol_clustering = self.analysis_results['spread_analysis']['autocorrelation_analysis']['volatility_clustering']
            if vol_clustering.get('has_clustering', False):
                risk_suggestions.append("检测到波动率聚集现象，建议在高波动期间降低仓位")

        # 基于参数稳定性
        if ('parameter_analysis' in self.analysis_results and
            'parameter_changes' in self.analysis_results['parameter_analysis']):

            param_changes = self.analysis_results['parameter_analysis']['parameter_changes']
            max_beta_change = param_changes.get('beta_change_max_abs', 0)

            if max_beta_change > 0.1:  # 10%以上的变化
                risk_suggestions.append(f"Beta系数最大变化率达{max_beta_change:.2%}，建议设置参数变化监控阈值")

        if not risk_suggestions:
            risk_suggestions.append("当前风险水平可控，建议保持现有风险管理策略")

        suggestions['risk_management'] = {
            'suggestions': risk_suggestions
        }

        for suggestion in risk_suggestions:
            print(f"   风险管理建议: {suggestion}")

        self.analysis_results['optimization_suggestions'] = suggestions
        return suggestions

    def create_visualizations(self):
        """
        创建分析结果的可视化图表
        """
        print("\n=== 生成可视化图表 ===")

        if self.spread_series is None:
            print("警告: 没有价差序列数据，跳过可视化")
            return

        # 创建图表
        fig, axes = plt.subplots(3, 3, figsize=(20, 15))
        fig.suptitle('ETF配对交易策略深度统计分析结果', fontsize=16, fontweight='bold')

        # 1. 价差序列时间序列图
        axes[0, 0].plot(self.spread_series.index, self.spread_series.values,
                       alpha=0.7, linewidth=0.8, color='blue')
        axes[0, 0].set_title('价差序列时间序列')
        axes[0, 0].set_xlabel('时间')
        axes[0, 0].set_ylabel('价差')
        axes[0, 0].grid(True, alpha=0.3)

        # 2. 价差序列分布直方图
        axes[0, 1].hist(self.spread_series.dropna(), bins=50, alpha=0.7,
                       density=True, color='skyblue', edgecolor='black')
        axes[0, 1].set_title('价差序列分布')
        axes[0, 1].set_xlabel('价差值')
        axes[0, 1].set_ylabel('密度')
        axes[0, 1].grid(True, alpha=0.3)

        # 添加正态分布拟合线
        try:
            mu, sigma = self.spread_series.mean(), self.spread_series.std()
            x = np.linspace(self.spread_series.min(), self.spread_series.max(), 100)
            y = stats.norm.pdf(x, mu, sigma)
            axes[0, 1].plot(x, y, 'r-', linewidth=2, label='正态分布拟合')
            axes[0, 1].legend()
        except:
            pass

        # 3. Q-Q图
        try:
            from scipy.stats import probplot
            probplot(self.spread_series.dropna(), dist="norm", plot=axes[0, 2])
            axes[0, 2].set_title('Q-Q图 (正态性检验)')
            axes[0, 2].grid(True, alpha=0.3)
        except Exception as e:
            axes[0, 2].text(0.5, 0.5, f'Q-Q图生成失败:\n{str(e)}',
                           transform=axes[0, 2].transAxes, ha='center', va='center')
            axes[0, 2].set_title('Q-Q图 (生成失败)')

        # 4. 自相关函数图
        if ('spread_analysis' in self.analysis_results and
            'autocorrelation_analysis' in self.analysis_results['spread_analysis'] and
            'autocorrelation' in self.analysis_results['spread_analysis']['autocorrelation_analysis']):

            acf_data = self.analysis_results['spread_analysis']['autocorrelation_analysis']['autocorrelation']
            acf_values = acf_data['acf_values']
            lags = range(len(acf_values))

            axes[1, 0].bar(lags, acf_values, alpha=0.7, color='green')
            axes[1, 0].axhline(y=0, color='black', linestyle='-', alpha=0.3)
            axes[1, 0].axhline(y=0.05, color='red', linestyle='--', alpha=0.5, label='5%显著性水平')
            axes[1, 0].axhline(y=-0.05, color='red', linestyle='--', alpha=0.5)
            axes[1, 0].set_title('自相关函数')
            axes[1, 0].set_xlabel('滞后期')
            axes[1, 0].set_ylabel('自相关系数')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        else:
            axes[1, 0].text(0.5, 0.5, '自相关数据不可用',
                           transform=axes[1, 0].transAxes, ha='center', va='center')
            axes[1, 0].set_title('自相关函数 (数据不可用)')

        # 5. Beta系数时间序列
        if self.beta_series is not None:
            axes[1, 1].plot(self.beta_series.index, self.beta_series.values,
                           alpha=0.7, linewidth=0.8, color='orange')
            axes[1, 1].set_title('Beta系数时间序列')
            axes[1, 1].set_xlabel('时间')
            axes[1, 1].set_ylabel('Beta值')
            axes[1, 1].grid(True, alpha=0.3)

            # 添加均值线
            mean_beta = self.beta_series.mean()
            axes[1, 1].axhline(y=mean_beta, color='red', linestyle='--',
                              alpha=0.7, label=f'均值: {mean_beta:.4f}')
            axes[1, 1].legend()
        else:
            axes[1, 1].text(0.5, 0.5, 'Beta数据不可用',
                           transform=axes[1, 1].transAxes, ha='center', va='center')
            axes[1, 1].set_title('Beta系数时间序列 (数据不可用)')

        # 6. 标准误差时间序列
        if self.std_error_series is not None:
            axes[1, 2].plot(self.std_error_series.index, self.std_error_series.values,
                           alpha=0.7, linewidth=0.8, color='purple')
            axes[1, 2].set_title('标准误差时间序列')
            axes[1, 2].set_xlabel('时间')
            axes[1, 2].set_ylabel('标准误差')
            axes[1, 2].grid(True, alpha=0.3)

            # 添加均值线
            mean_std_error = self.std_error_series.mean()
            axes[1, 2].axhline(y=mean_std_error, color='red', linestyle='--',
                              alpha=0.7, label=f'均值: {mean_std_error:.6f}')
            axes[1, 2].legend()
        else:
            axes[1, 2].text(0.5, 0.5, '标准误差数据不可用',
                           transform=axes[1, 2].transAxes, ha='center', va='center')
            axes[1, 2].set_title('标准误差时间序列 (数据不可用)')

        # 7. Beta分布直方图
        if self.beta_series is not None:
            axes[2, 0].hist(self.beta_series.dropna(), bins=30, alpha=0.7,
                           color='orange', edgecolor='black')
            axes[2, 0].set_title('Beta系数分布')
            axes[2, 0].set_xlabel('Beta值')
            axes[2, 0].set_ylabel('频数')
            axes[2, 0].grid(True, alpha=0.3)
        else:
            axes[2, 0].text(0.5, 0.5, 'Beta数据不可用',
                           transform=axes[2, 0].transAxes, ha='center', va='center')
            axes[2, 0].set_title('Beta系数分布 (数据不可用)')

        # 8. 标准误差分布直方图
        if self.std_error_series is not None:
            axes[2, 1].hist(self.std_error_series.dropna(), bins=30, alpha=0.7,
                           color='purple', edgecolor='black')
            axes[2, 1].set_title('标准误差分布')
            axes[2, 1].set_xlabel('标准误差值')
            axes[2, 1].set_ylabel('频数')
            axes[2, 1].grid(True, alpha=0.3)
        else:
            axes[2, 1].text(0.5, 0.5, '标准误差数据不可用',
                           transform=axes[2, 1].transAxes, ha='center', va='center')
            axes[2, 1].set_title('标准误差分布 (数据不可用)')

        # 9. 参数稳定性分析图
        if (self.beta_series is not None and self.std_error_series is not None and
            len(self.beta_series) > 60):

            # 计算60分钟滚动标准差
            beta_rolling_std = self.beta_series.rolling(window=60).std()
            std_error_rolling_std = self.std_error_series.rolling(window=60).std()

            ax2_1 = axes[2, 2]
            ax2_2 = ax2_1.twinx()

            line1 = ax2_1.plot(beta_rolling_std.index, beta_rolling_std.values,
                              color='orange', alpha=0.7, label='Beta滚动标准差')
            line2 = ax2_2.plot(std_error_rolling_std.index, std_error_rolling_std.values,
                              color='purple', alpha=0.7, label='标准误差滚动标准差')

            ax2_1.set_xlabel('时间')
            ax2_1.set_ylabel('Beta滚动标准差', color='orange')
            ax2_2.set_ylabel('标准误差滚动标准差', color='purple')
            ax2_1.set_title('参数稳定性分析 (60分钟滚动标准差)')

            # 合并图例
            lines = line1 + line2
            labels = [l.get_label() for l in lines]
            ax2_1.legend(lines, labels, loc='upper left')

            ax2_1.grid(True, alpha=0.3)
        else:
            axes[2, 2].text(0.5, 0.5, '参数稳定性数据不足',
                           transform=axes[2, 2].transAxes, ha='center', va='center')
            axes[2, 2].set_title('参数稳定性分析 (数据不足)')

        # 调整布局
        plt.tight_layout()

        # 保存图表
        plot_file = os.path.join(self.output_dir, 'statistical_analysis_plots.png')
        plt.savefig(plot_file, dpi=300, bbox_inches='tight')
        print(f"可视化图表已保存: {plot_file}")

        plt.show()

        return plot_file

    def generate_comprehensive_report(self) -> str:
        """
        生成综合统计分析报告

        Returns:
            报告文件路径
        """
        print("\n=== 生成综合分析报告 ===")

        report_lines = []
        report_lines.append("# ETF配对交易策略深度统计分析报告")
        report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("=" * 80)
        report_lines.append("")

        # 1. 执行摘要
        report_lines.append("## 1. 执行摘要")
        if self.spread_series is not None:
            report_lines.append(f"- 分析数据点: {len(self.spread_series)} 个")
            report_lines.append(f"- 价差均值: {self.spread_series.mean():.6f}")
            report_lines.append(f"- 价差标准差: {self.spread_series.std():.6f}")

        # 添加关键发现
        key_findings = []

        # 白噪声检验结果
        if ('spread_analysis' in self.analysis_results and
            'white_noise_tests' in self.analysis_results['spread_analysis']):
            white_noise = self.analysis_results['spread_analysis']['white_noise_tests']
            if 'ljung_box' in white_noise:
                is_white_noise = all(result.get('is_white_noise', True)
                                   for result in white_noise['ljung_box'].values())
                key_findings.append(f"价差序列白噪声特征: {'符合' if is_white_noise else '不符合'}")

        # 正态性检验结果
        if ('spread_analysis' in self.analysis_results and
            'distribution_analysis' in self.analysis_results['spread_analysis'] and
            'normality_tests' in self.analysis_results['spread_analysis']['distribution_analysis']):
            normality = self.analysis_results['spread_analysis']['distribution_analysis']['normality_tests']
            normal_count = sum(1 for test in normality.values() if test.get('is_normal', False))
            total_tests = len(normality)
            key_findings.append(f"正态性检验: {normal_count}/{total_tests} 项检验支持正态分布")

        # 参数稳定性
        if ('parameter_analysis' in self.analysis_results and
            'beta_analysis' in self.analysis_results['parameter_analysis']):
            beta_cv = self.analysis_results['parameter_analysis']['beta_analysis'].get('cv', 0)
            stability = "高" if beta_cv < 0.01 else "中" if beta_cv < 0.05 else "低"
            key_findings.append(f"Beta系数稳定性: {stability} (变异系数: {beta_cv:.4f})")

        for finding in key_findings:
            report_lines.append(f"- {finding}")

        report_lines.append("")

        # 2. 价差序列统计特性分析
        report_lines.append("## 2. 价差序列统计特性分析")

        if ('spread_analysis' in self.analysis_results and
            'distribution_analysis' in self.analysis_results['spread_analysis']):
            dist_analysis = self.analysis_results['spread_analysis']['distribution_analysis']

            if 'descriptive_stats' in dist_analysis:
                stats_data = dist_analysis['descriptive_stats']
                report_lines.append("### 2.1 描述性统计")
                report_lines.append(f"- 样本数量: {stats_data['count']}")
                report_lines.append(f"- 均值: {stats_data['mean']:.6f}")
                report_lines.append(f"- 标准差: {stats_data['std']:.6f}")
                report_lines.append(f"- 偏度: {stats_data['skewness']:.4f}")
                report_lines.append(f"- 峰度: {stats_data['kurtosis']:.4f}")
                report_lines.append(f"- 中位数: {stats_data['median']:.6f}")
                report_lines.append("")

            if 'normality_tests' in dist_analysis:
                report_lines.append("### 2.2 正态性检验结果")
                normality = dist_analysis['normality_tests']

                for test_name, test_result in normality.items():
                    test_display_name = {
                        'shapiro_wilk': 'Shapiro-Wilk检验',
                        'kolmogorov_smirnov': 'Kolmogorov-Smirnov检验',
                        'jarque_bera': 'Jarque-Bera检验'
                    }.get(test_name, test_name)

                    report_lines.append(f"- {test_display_name}:")
                    report_lines.append(f"  - 统计量: {test_result['statistic']:.4f}")
                    report_lines.append(f"  - p值: {test_result['p_value']:.4f}")
                    report_lines.append(f"  - 正态分布: {'是' if test_result['is_normal'] else '否'}")

                report_lines.append("")

        if ('spread_analysis' in self.analysis_results and
            'white_noise_tests' in self.analysis_results['spread_analysis']):
            report_lines.append("### 2.3 白噪声检验结果")
            white_noise = self.analysis_results['spread_analysis']['white_noise_tests']

            if 'ljung_box' in white_noise:
                report_lines.append("#### Ljung-Box检验 (序列相关性):")
                for lag, result in white_noise['ljung_box'].items():
                    report_lines.append(f"- {lag}: 统计量={result['statistic']:.4f}, "
                                      f"p值={result['p_value']:.4f}, "
                                      f"白噪声={'是' if result['is_white_noise'] else '否'}")

            if 'adf_test' in white_noise:
                adf = white_noise['adf_test']
                report_lines.append("#### ADF平稳性检验:")
                report_lines.append(f"- 统计量: {adf['statistic']:.4f}")
                report_lines.append(f"- p值: {adf['p_value']:.4f}")
                report_lines.append(f"- 平稳性: {'是' if adf['is_stationary'] else '否'}")

            if 'kpss_test' in white_noise:
                kpss = white_noise['kpss_test']
                report_lines.append("#### KPSS平稳性检验:")
                report_lines.append(f"- 统计量: {kpss['statistic']:.4f}")
                report_lines.append(f"- p值: {kpss['p_value']:.4f}")
                report_lines.append(f"- 平稳性: {'是' if kpss['is_stationary'] else '否'}")

            report_lines.append("")

        # 3. 回归参数时变性分析
        report_lines.append("## 3. 回归参数时变性分析")

        if 'parameter_analysis' in self.analysis_results:
            param_analysis = self.analysis_results['parameter_analysis']

            if 'beta_analysis' in param_analysis:
                beta_stats = param_analysis['beta_analysis']
                report_lines.append("### 3.1 Beta系数分析")
                report_lines.append(f"- 均值: {beta_stats['mean']:.6f}")
                report_lines.append(f"- 标准差: {beta_stats['std']:.6f}")
                report_lines.append(f"- 变异系数: {beta_stats['cv']:.4f}")
                report_lines.append(f"- 变化范围: {beta_stats['range']:.6f}")
                if 'is_stationary' in beta_stats:
                    report_lines.append(f"- 平稳性: {'是' if beta_stats['is_stationary'] else '否'}")
                report_lines.append("")

            if 'std_error_analysis' in param_analysis:
                std_error_stats = param_analysis['std_error_analysis']
                report_lines.append("### 3.2 标准误差分析")
                report_lines.append(f"- 均值: {std_error_stats['mean']:.6f}")
                report_lines.append(f"- 标准差: {std_error_stats['std']:.6f}")
                report_lines.append(f"- 变异系数: {std_error_stats['cv']:.4f}")
                report_lines.append(f"- 变化范围: {std_error_stats['range']:.6f}")
                if 'is_stationary' in std_error_stats:
                    report_lines.append(f"- 平稳性: {'是' if std_error_stats['is_stationary'] else '否'}")
                report_lines.append("")

            if 'parameter_changes' in param_analysis:
                changes = param_analysis['parameter_changes']
                report_lines.append("### 3.3 参数变化率分析")
                report_lines.append(f"- Beta平均变化率: {changes['beta_change_mean']:.6f}")
                report_lines.append(f"- Beta最大绝对变化率: {changes['beta_change_max_abs']:.6f}")
                report_lines.append(f"- 标准误差平均变化率: {changes['std_error_change_mean']:.6f}")
                report_lines.append(f"- 标准误差最大绝对变化率: {changes['std_error_change_max_abs']:.6f}")
                report_lines.append("")

        # 4. 优化建议
        report_lines.append("## 4. 优化建议")

        if 'optimization_suggestions' in self.analysis_results:
            suggestions = self.analysis_results['optimization_suggestions']

            if 'parameter_update_frequency' in suggestions:
                freq_suggestion = suggestions['parameter_update_frequency']
                report_lines.append("### 4.1 参数更新频率建议")
                report_lines.append(f"- 建议频率: {freq_suggestion['recommended_frequency']}")
                report_lines.append(f"- 理由: {freq_suggestion['reason']}")
                report_lines.append("")

            if 'signal_quality' in suggestions:
                signal_suggestion = suggestions['signal_quality']
                report_lines.append("### 4.2 交易信号质量评估")
                report_lines.append(f"- 评估结果: {signal_suggestion['assessment']}")
                report_lines.append("")

            if 'distribution_optimization' in suggestions:
                dist_suggestions = suggestions['distribution_optimization']['suggestions']
                report_lines.append("### 4.3 分布特征优化建议")
                for suggestion in dist_suggestions:
                    report_lines.append(f"- {suggestion}")
                report_lines.append("")

            if 'risk_management' in suggestions:
                risk_suggestions = suggestions['risk_management']['suggestions']
                report_lines.append("### 4.4 风险管理建议")
                for suggestion in risk_suggestions:
                    report_lines.append(f"- {suggestion}")
                report_lines.append("")

        # 5. 结论
        report_lines.append("## 5. 结论")
        report_lines.append("基于以上统计分析结果，我们得出以下主要结论：")
        report_lines.append("")

        conclusions = []

        # 基于分析结果生成结论
        if ('spread_analysis' in self.analysis_results and
            'white_noise_tests' in self.analysis_results['spread_analysis']):
            white_noise = self.analysis_results['spread_analysis']['white_noise_tests']
            if 'ljung_box' in white_noise:
                is_white_noise = all(result.get('is_white_noise', True)
                                   for result in white_noise['ljung_box'].values())
                if is_white_noise:
                    conclusions.append("价差序列基本符合白噪声假设，表明当前交易策略的信号质量较好")
                else:
                    conclusions.append("价差序列存在序列相关性，建议进一步优化策略参数或考虑更复杂的模型")

        if ('parameter_analysis' in self.analysis_results and
            'beta_analysis' in self.analysis_results['parameter_analysis']):
            beta_cv = self.analysis_results['parameter_analysis']['beta_analysis'].get('cv', 0)
            if beta_cv < 0.01:
                conclusions.append("Beta系数表现出高度稳定性，支持降低参数更新频率以减少计算成本")
            elif beta_cv > 0.05:
                conclusions.append("Beta系数波动较大，建议保持较高的参数更新频率以捕捉市场变化")

        if not conclusions:
            conclusions.append("当前策略参数设置基本合理，建议继续监控关键指标的变化")

        for i, conclusion in enumerate(conclusions, 1):
            report_lines.append(f"{i}. {conclusion}")

        report_lines.append("")
        report_lines.append("---")
        report_lines.append("报告生成完成。建议定期重新进行统计分析以监控策略性能。")

        # 保存报告
        report_file = os.path.join(self.output_dir, 'statistical_analysis_report.md')
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))

        print(f"综合分析报告已保存: {report_file}")
        return report_file

    def run_complete_analysis(self, signals_file_path: str) -> Dict:
        """
        运行完整的统计分析流程

        Args:
            signals_file_path: 信号文件路径

        Returns:
            完整分析结果
        """
        print("开始ETF配对交易策略深度统计分析...")
        print("=" * 80)

        try:
            # 1. 加载数据
            self.load_signals_data(signals_file_path)

            # 2. 计算价差序列
            self.calculate_spread_series()

            # 3. 执行各项统计分析
            self.white_noise_tests()
            self.distribution_analysis()
            self.autocorrelation_analysis()
            self.parameter_stability_analysis()

            # 4. 生成优化建议
            self.generate_optimization_suggestions()

            # 5. 生成可视化图表
            self.create_visualizations()

            # 6. 生成综合报告
            self.generate_comprehensive_report()

            print("\n" + "=" * 80)
            print("统计分析完成！")
            print(f"结果保存在目录: {self.output_dir}")

            return self.analysis_results

        except Exception as e:
            print(f"分析过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """
    主函数 - 演示如何使用ETF统计分析器
    """
    print("ETF配对交易策略深度统计分析工具")
    print("=" * 50)

    # 查找可用的信号文件
    results_dir = "results"
    if not os.path.exists(results_dir):
        print(f"错误: 结果目录 {results_dir} 不存在")
        print("请先运行配对交易策略生成信号文件")
        return

    # 查找信号文件
    signal_files = [f for f in os.listdir(results_dir) if f.startswith('signals_') and f.endswith('.csv')]

    if not signal_files:
        print(f"错误: 在 {results_dir} 目录中未找到信号文件")
        print("请先运行配对交易策略生成信号文件")
        return

    print(f"找到 {len(signal_files)} 个信号文件:")
    for i, file in enumerate(signal_files, 1):
        print(f"{i}. {file}")

    # 选择要分析的文件
    if len(signal_files) == 1:
        selected_file = signal_files[0]
        print(f"\n自动选择唯一的信号文件: {selected_file}")
    else:
        try:
            choice = input(f"\n请选择要分析的信号文件 (1-{len(signal_files)}): ").strip()
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(signal_files):
                selected_file = signal_files[choice_idx]
            else:
                print("无效选择，使用第一个文件")
                selected_file = signal_files[0]
        except (ValueError, KeyboardInterrupt):
            print("使用第一个文件")
            selected_file = signal_files[0]

    signals_file_path = os.path.join(results_dir, selected_file)
    print(f"选择的信号文件: {signals_file_path}")

    # 创建分析器并运行分析
    try:
        analyzer = ETFStatisticalAnalyzer()
        results = analyzer.run_complete_analysis(signals_file_path)

        print("\n" + "=" * 50)
        print("分析完成！主要结果:")

        # 显示关键结果摘要
        if 'spread_analysis' in results:
            spread_analysis = results['spread_analysis']

            if 'distribution_analysis' in spread_analysis:
                dist_stats = spread_analysis['distribution_analysis'].get('descriptive_stats', {})
                print(f"价差序列统计: 均值={dist_stats.get('mean', 0):.6f}, "
                      f"标准差={dist_stats.get('std', 0):.6f}")

            if 'white_noise_tests' in spread_analysis:
                white_noise = spread_analysis['white_noise_tests']
                if 'ljung_box' in white_noise:
                    lb_results = white_noise['ljung_box']
                    white_noise_count = sum(1 for result in lb_results.values()
                                          if result.get('is_white_noise', False))
                    print(f"白噪声检验: {white_noise_count}/{len(lb_results)} 项检验支持白噪声假设")

        if 'parameter_analysis' in results:
            param_analysis = results['parameter_analysis']
            if 'beta_analysis' in param_analysis:
                beta_cv = param_analysis['beta_analysis'].get('cv', 0)
                stability = "高" if beta_cv < 0.01 else "中" if beta_cv < 0.05 else "低"
                print(f"Beta系数稳定性: {stability} (变异系数: {beta_cv:.4f})")

        if 'optimization_suggestions' in results:
            suggestions = results['optimization_suggestions']
            if 'parameter_update_frequency' in suggestions:
                freq = suggestions['parameter_update_frequency']['recommended_frequency']
                print(f"建议参数更新频率: {freq}")

        print(f"\n详细结果请查看: {analyzer.output_dir}")

    except Exception as e:
        print(f"分析失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
